#!/usr/bin/env python3
"""
triangle_screen_threshold.py — 评分并筛选出现 ≥0.1 % 三角套利机会的币
加入：API 超时 / 连接重置自动重试（默认 5 次指数退避）

核心公式
    score = faces × log10(volume+1) × σ × depth_factor × hit_rate
"""

from __future__ import annotations
import argparse, time, math, sys, traceback
from collections import defaultdict
import json
import os

import requests

BINANCE = "https://api.binance.com"
QUOTE_WHITELIST = {"USDT", "BTC", "ETH", "BNB", "FDUSD", "USDC"}
DEPTH_PRICE_RANGE = 0.0005  # ±0.05 %
MIN_DEPTH_USD = 10_000  # 低于此深度按比例打折

# 缓存配置
CACHE_FILE = "price_tick_cache.json"
CACHE_EXPIRE_HOURS = 24  # 缓存24小时后过期


# ──────────────────────── 通用请求封装 ────────────────────────
def fetch(path: str, retries=5, backoff=5, **params):
    """带重试的 GET。retries 次失败后抛异常"""
    for attempt in range(1, retries + 1):
        try:
            r = requests.get(f"{BINANCE}{path}", params=params, timeout=15)  # 15 s 超时
            r.raise_for_status()
            return r.json()
        except Exception as e:
            if attempt == retries:
                raise  # 最后一次仍失败，让上层决定
            wait = backoff * attempt
            print(
                f"    ⚠️ API 调用失败：{e} | 第 {attempt}/{retries} 次重试… " f"等待 {wait} 秒",
                file=sys.stderr,
                flush=True,
            )
            time.sleep(wait)


# 简单包装原有接口
exchange_info = lambda **kw: fetch("/api/v3/exchangeInfo", **kw)["symbols"]
stats24 = lambda **kw: fetch("/api/v3/ticker/24hr", **kw)
depth_snapshot = lambda symbol, **kw: fetch("/api/v3/depth", symbol=symbol, limit=1000, **kw)


def klines(symbol: str, start_ms: int, end_ms: int, sleep_sec: float, retries: int, backoff: int):
    """拉取 [start_ms, end_ms) 的 1 min K 线，内部也带重试"""
    out = []
    while start_ms < end_ms:
        batch = fetch(
            "/api/v3/klines",
            symbol=symbol,
            interval="1m",
            startTime=start_ms,
            endTime=min(end_ms, start_ms + 1000 * 60_000),
            limit=1000,
            retries=retries,
            backoff=backoff,
        )
        if not batch:
            break
        out.extend(float(c[4]) for c in batch)
        start_ms = batch[-1][0] + 60_000
        time.sleep(sleep_sec)
    return out


# ────────────────────────── 工具函数 ──────────────────────────
def usd_depth(snapshot, mid, usd_per_quote=1.0):
    rng = DEPTH_PRICE_RANGE
    bid_sum = 0.0
    for price, qty in snapshot["bids"]:
        p = float(price)
        if p < mid * (1 - rng):
            break
        bid_sum += float(qty) * p * usd_per_quote
    ask_sum = 0.0
    for price, qty in snapshot["asks"]:
        p = float(price)
        if p > mid * (1 + rng):
            break
        ask_sum += float(qty) * p * usd_per_quote
    return min(bid_sum, ask_sum)


def stdev(vals):
    n = len(vals)
    if n < 2:
        return 0.0
    m = sum(vals) / n
    return (sum((x - m) ** 2 for x in vals) / (n - 1)) ** 0.5


# ────────────────────────── 缓存管理 ──────────────────────────
def load_price_tick_cache():
    """加载 price_tick 占比缓存"""
    if not os.path.exists(CACHE_FILE):
        return {}

    try:
        with open(CACHE_FILE, "r") as f:
            cache_data = json.load(f)

        # 检查缓存是否过期
        cache_time = cache_data.get("timestamp", 0)
        current_time = time.time()
        if current_time - cache_time > CACHE_EXPIRE_HOURS * 3600:
            print(f">>> 缓存已过期 ({CACHE_EXPIRE_HOURS}小时)，将重新获取数据")
            return {}

        print(f">>> 加载缓存数据，包含 {len(cache_data.get('ratios', {}))} 个交易对")
        return cache_data.get("ratios", {})
    except Exception as e:
        print(f">>> 加载缓存失败: {e}，将重新获取数据")
        return {}


def save_price_tick_cache(ratios):
    """保存 price_tick 占比缓存"""
    try:
        cache_data = {"timestamp": time.time(), "ratios": ratios}
        with open(CACHE_FILE, "w") as f:
            json.dump(cache_data, f, indent=2)
        print(f">>> 已保存 {len(ratios)} 个交易对的 price_tick 占比到缓存")
    except Exception as e:
        print(f">>> 保存缓存失败: {e}")


def get_price_tick_ratio(symbol, price_tick, cache, retries=3, backoff=5):
    """获取 price_tick 占比，优先使用缓存"""
    # 先检查缓存
    if symbol in cache:
        return cache[symbol]

    # 缓存中没有，从 API 获取
    for attempt in range(1, retries + 1):
        try:
            current_price_data = fetch("/api/v3/ticker/price", symbol=symbol, retries=1, backoff=1)
            current_price = float(current_price_data["price"])
            if current_price > 0:
                ratio = price_tick / current_price
                cache[symbol] = ratio  # 更新缓存
                return ratio
            return None
        except Exception as e:
            if attempt == retries:
                print(f"    获取 {symbol} 价格失败: {e}")
                return None
            time.sleep(backoff * attempt)


# ────────────────────────── 主流程 ────────────────────────────
def main():
    p = argparse.ArgumentParser()
    p.add_argument("--threshold", type=float, default=0.001, help="最小可执行价差 (0.001 = 0.1%%)")
    p.add_argument("--days", type=int, default=7)
    p.add_argument("--limit", type=int, default=50, help="先用成交额粗筛的币数")
    p.add_argument("--top", type=int, default=20)
    p.add_argument("--sleep", type=float, default=0.08, help="每 1000 根 kline 拉完后的 sleep 秒数")
    p.add_argument("--retries", type=int, default=5, help="API 调用最大重试次数")
    p.add_argument("--backoff", type=int, default=5, help="第一次重试等待秒数 (随后指数增加)")
    p.add_argument("--refresh-cache", action="store_true", help="强制刷新 price_tick 占比缓存")
    args = p.parse_args()
    # 固定使用最近 7 天窗口（忽略传入的 --days）
    args.days = 7

    print(">>> 拉取交易所元数据 …")
    symbols = exchange_info(retries=args.retries, backoff=args.backoff)
    print(f">>> 获取到 {len(symbols)} 个交易对")

    # 加载 price_tick 占比缓存
    print(">>> 加载 price_tick 占比缓存 …")
    if args.refresh_cache:
        print(">>> 强制刷新缓存模式")
        price_tick_cache = {}
    else:
        price_tick_cache = load_price_tick_cache()

    print(">>> 开始过滤 price_tick 占比 …")
    base_to_quotes, sym_name = defaultdict(set), {}
    filtered_count = 0

    for s in symbols:
        if s["status"] != "TRADING":
            continue

        # 检查 price_tick 占比条件
        price_tick = None
        for filter_info in s.get("filters", []):
            if filter_info.get("filterType") == "PRICE_FILTER":
                price_tick = float(filter_info.get("tickSize", "0"))
                break

        if price_tick and price_tick > 0:
            # 使用缓存获取 price_tick 占比
            tick_ratio = get_price_tick_ratio(
                s["symbol"], price_tick, price_tick_cache, retries=args.retries, backoff=args.backoff
            )

            if tick_ratio is None:
                # 如果获取价格失败，跳过这个交易对
                print(f"    跳过 {s['symbol']}: 无法获取价格")
                filtered_count += 1
                continue

            if tick_ratio >= 0.0005:  # 如果占比 >= 0.05%，跳过这个交易对
                print(f"    跳过 {s['symbol']}: price_tick占比 {tick_ratio:.6f} >= 0.0005")
                filtered_count += 1
                continue

        b, q = s["baseAsset"], s["quoteAsset"]
        base_to_quotes[b].add(q)
        sym_name[(b, q)] = s["symbol"]

    print(f">>> 过滤掉 {filtered_count} 个交易对，剩余 {len(sym_name)} 个交易对")

    # 保存更新后的缓存
    save_price_tick_cache(price_tick_cache)
    print(">>> 拉取 24 h 成交额 …")
    vol_map = {s["symbol"]: float(s["quoteVolume"]) for s in stats24(retries=args.retries, backoff=args.backoff)}

    # ① 粗筛（Plan C：按可组成的三环数量 tri_count × 成交额进行筛选）
    cand = []
    for base, qs in base_to_quotes.items():
        usable = qs & QUOTE_WHITELIST
        if len(usable) < 2:
            continue
        # 计算存在交叉市场的报价币对数（三环数）
        tri_pairs = []
        ulist = sorted(list(usable))
        for i in range(len(ulist)):
            for j in range(i + 1, len(ulist)):
                q1, q2 = ulist[i], ulist[j]
                if (q1, q2) in sym_name or (q2, q1) in sym_name:
                    tri_pairs.append((q1, q2))
        tri_count = len(tri_pairs)
        if tri_count == 0:
            continue
        vol_sum = sum(vol_map.get(sym_name[(base, q)], 0) for q in usable)
        basic = tri_count * math.log10(vol_sum + 1)
        cand.append((base, usable, vol_sum, basic, tri_pairs))
    cand.sort(key=lambda x: x[3], reverse=True)
    cand = cand[: args.limit]
    print(f">>> 第一轮筛后深分析 {len(cand)} 个币")

    end_ms = int(time.time() * 1000)
    start_ms = end_ms - args.days * 24 * 60 * 60 * 1000
    # 构建部分常见报价币到 USD 的估值（用于将深度换算到 USD）
    quote_usd = {"USDT": 1.0, "USDC": 1.0, "FDUSD": 1.0}
    # 获取 BTC、ETH、BNB 的 USD 价格
    for sym, name in [("BTCUSDT", "BTC"), ("ETHUSDT", "ETH"), ("BNBUSDT", "BNB")]:
        try:
            quote_usd[name] = float(
                fetch("/api/v3/ticker/price", symbol=sym, retries=args.retries, backoff=args.backoff)["price"]
            )
        except Exception:
            pass

    results = []
    for idx, (base, qs, vol_sum, basic, tri_pairs) in enumerate(cand, 1):
        print(f"[{idx:02}/{len(cand)}] 处理 {base} …", flush=True)

        # 该 base 的三环数量与“每币可产环数”效率
        tri_count = len(tri_pairs)
        coins_count = 1 + len(qs)
        tri_eff = tri_count / coins_count

        # 本 base 级别缓存，避免重复请求
        kline_cache = {}
        depth_cache = {}

        for q1, q2 in tri_pairs:
            leg1 = sym_name[(base, q1)]
            leg2 = sym_name[(base, q2)]
            # 交叉腿必然存在（由 tri_pairs 构建时保证）
            leg3 = sym_name.get((q1, q2)) or sym_name.get((q2, q1))

            # --- 2.1 K 线 -----------------------------------------------------
            try:
                if leg1 in kline_cache:
                    p_b_q1 = kline_cache[leg1]
                else:
                    p_b_q1 = klines(leg1, start_ms, end_ms, args.sleep, args.retries, args.backoff)
                    kline_cache[leg1] = p_b_q1
                if leg2 in kline_cache:
                    p_b_q2 = kline_cache[leg2]
                else:
                    p_b_q2 = klines(leg2, start_ms, end_ms, args.sleep, args.retries, args.backoff)
                    kline_cache[leg2] = p_b_q2
                if leg3 in kline_cache:
                    p_cross = kline_cache[leg3]
                else:
                    p_cross = klines(leg3, start_ms, end_ms, args.sleep, args.retries, args.backoff)
                    kline_cache[leg3] = p_cross
            except Exception as e:
                print(f"    ❌ kline 多次失败：{e}")
                continue

            m = min(len(p_b_q1), len(p_b_q2), len(p_cross))
            if m < 200:
                print("    ⚠️ 数据点不足 (<200)，跳过该三环")
                continue

            # 判断 leg3 的方向：q1/q2 还是 q2/q1
            if leg3.startswith(q1):  # q1q2, 价格= q1 in q2
                price_q1_q2 = p_cross
            else:  # q2q1, 先取倒数
                price_q1_q2 = [1 / x for x in p_cross]

            # 计算 spread（取绝对值，这样正/反方向都算）
            spreads = [abs(p_b_q1[i] / (p_b_q2[i] * price_q1_q2[i]) - 1) for i in range(m)]
            sigma = stdev(spreads)  # 周期固定 7 天，sigma 代表周内三角套利价差波动率
            hits = sum(1 for x in spreads if x > args.threshold)
            hit_rate = hits / m
            if hit_rate == 0:
                # 该三环从未超过阈值，跳过
                continue

            # 2.2 深度
            try:
                if leg1 in depth_cache:
                    d1 = depth_cache[leg1]
                else:
                    d1 = depth_snapshot(leg1, retries=args.retries, backoff=args.backoff)
                    depth_cache[leg1] = d1
                if leg2 in depth_cache:
                    d2 = depth_cache[leg2]
                else:
                    d2 = depth_snapshot(leg2, retries=args.retries, backoff=args.backoff)
                    depth_cache[leg2] = d2
                if leg3 in depth_cache:
                    d3 = depth_cache[leg3]
                else:
                    d3 = depth_snapshot(leg3, retries=args.retries, backoff=args.backoff)
                    depth_cache[leg3] = d3
            except Exception as e:
                print(f"    ❌ depth 多次失败：{e}")
                traceback.print_exc(limit=1)
                continue
            mid1 = (float(d1["bids"][0][0]) + float(d1["asks"][0][0])) / 2
            mid2 = (float(d2["bids"][0][0]) + float(d2["asks"][0][0])) / 2
            mid3 = (float(d3["bids"][0][0]) + float(d3["asks"][0][0])) / 2
            # 将三条腿深度统一换算为 USD
            usd_per_q1 = quote_usd.get(q1, 1.0)
            usd_per_q2 = quote_usd.get(q2, 1.0)
            # 交叉腿的计价币是 leg3 的报价币；根据符号推断：若 leg3 == q1q2，则价格单位为 q2；若 leg3 == q2q1，则为 q1
            cross_quote = q2 if sym_name.get((q1, q2)) else q1
            usd_per_cross_quote = quote_usd.get(cross_quote, 1.0)

            dep1 = usd_depth(d1, mid1, usd_per_quote=usd_per_q1)
            dep2 = usd_depth(d2, mid2, usd_per_quote=usd_per_q2)
            dep3 = usd_depth(d3, mid3, usd_per_quote=usd_per_cross_quote)
            min_dep = min(dep1, dep2, dep3)
            depth_factor = min(1.0, min_dep / MIN_DEPTH_USD)

            # Plan C 倾向：三环/币数效率 × 成交额规模
            plan_c_basic = tri_eff * math.log10(vol_sum + 1)
            score = plan_c_basic * sigma * depth_factor * hit_rate

            results.append(
                {
                    "base": base,
                    "tri_count": tri_count,
                    "coins": coins_count,
                    "vol": vol_sum,
                    "sigma": sigma,
                    "hit": hit_rate,
                    "depth": min_dep,
                    "score": score,
                    "triangle": (leg1, leg2, leg3),
                }
            )

    # ③ 输出
    results.sort(key=lambda r: r["score"], reverse=True)
    top = results[: args.top]
    print("\n=========== 最终结果 ===========")
    print(f"阈值: {args.threshold:.3%} | 窗口: {args.days} d | 输出: Top {args.top}\n")
    header = "{:<8} {:>8} {:>5} {:>12} {:>8} {:>7} {:>9} {:>9}".format(
        "Base", "TriCnt", "Coin#", "24hVolM", "σ(week)", "Hit%", "Depth$", "Score"
    )
    print(header)
    print("-" * len(header))
    for r in top:
        print(
            "{:<8} {:>8} {:>5} {:>12.1f} {:>8.4f} {:>7.2%} {:>9.0f} {:>9.2f}".format(
                r["base"],
                r.get("tri_count", 0),
                r.get("coins", 0),
                r["vol"] / 1_000_000,
                r["sigma"],
                r["hit"],
                r["depth"],
                r["score"],
            )
        )
        print("    ", " ↔ ".join(r["triangle"]))

    print("\n全部完成。")


if __name__ == "__main__":
    main()
