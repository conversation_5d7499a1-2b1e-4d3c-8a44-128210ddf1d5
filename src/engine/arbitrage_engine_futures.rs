use crate::{
    encoding::{
        book_ticker::FuturesBookTicker, depth_snapshot::FuturesDepthSnapshot,
        depth_update::FuturesDepthUpdate,
    },
    engine::{
        futures_agg_traders_aggregator::FuturesAggTradeAggregator,
        futures_orderbook::FuturesOrderBook,
    },
};

pub struct ArbitrageEngineFutures {
    pub orderbook: FuturesOrderBook<20>,
    pub agg_traders: FuturesAggTradeAggregator<50>, // 使用const泛型，最大50个聚合数据
}

impl ArbitrageEngineFutures {
    pub fn new() -> Self {
        Self {
            orderbook: FuturesOrderBook::new(),
            agg_traders: FuturesAggTradeAggregator::new(), // 不再需要参数
        }
    }

    pub fn update_orderbook_snapshot(&mut self, depth_snapshot: &FuturesDepthSnapshot) {
        self.orderbook.apply_snapshot(depth_snapshot);
    }

    pub fn update_orderbook_diff(&mut self, depth_diff: &FuturesDepthUpdate) {
        self.orderbook.apply_diff(depth_diff);
    }

    pub fn update_book_ticker(&mut self, book_ticker: &FuturesBookTicker) {
        self.orderbook.apply_book_ticker(book_ticker);
    }
}
