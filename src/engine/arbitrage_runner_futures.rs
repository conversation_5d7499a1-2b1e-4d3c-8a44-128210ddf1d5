use crate::{
    CallbackData, Message, Result, Settings, WebSocket, WebSocketHandle,
    encoding::{
        agg_trades::parse_futures_agg_trade, book_ticker::parse_futures_bookticker,
        depth_snapshot::parse_futures_depth_snapshot, depth_update::parse_futures_depth_update,
        sbe::trade::parse_sbe_trades,
    },
    engine::{
        arbitrage_engine::ArbitrageEngine,
        arbitrage_engine_futures::ArbitrageEngineFutures,
        binance::{
            generate_futures_agg_trade_url, generate_futures_book_ticker_url,
            generate_futures_depth_diff_url, generate_futures_depth_snapshot_url,
            generate_futures_order_url,
        },
        token::*,
        trade::{
            generate_futures_session_logon_request, generate_futures_user_data_sub_request,
            generate_session_logon_request, update_trading_fee,
        },
        trading_pair::API_KEY,
    },
    error, flush_logs, info, logln,
    net::utils::url::Url,
};

const IN_LEN: usize = 1024 * 32;
const OUT_LEN: usize = 1024 * 4;
const SYMBOL: &str = "TRUMPUSDC";

pub fn callback() -> impl FnMut(&mut WebSocketHandle<IN_LEN, OUT_LEN>, CallbackData) -> Result<()> {
    let mut arbitrage_engine = ArbitrageEngineFutures::new();
    let mut user_data_stream_subscribed = false;

    move |handle: &mut WebSocketHandle<IN_LEN, OUT_LEN>, cd: CallbackData| -> Result<()> {
        match cd {
            CallbackData::Message(token, msg) => match msg {
                Message::WebsocketPayload(data) => match token {
                    WS_BBO_1 => {
                        if let Some(bt) = parse_futures_bookticker(data.as_ref()) {
                            info!("book ticker: {:?}", bt);
                        } else {
                            info!(
                                "failed to parse bbo: {}",
                                String::from_utf8_lossy(data.as_ref())
                            );
                        }
                    }
                    WS_DEPTH_SS_1 => {
                        if let Some(depth) = parse_futures_depth_snapshot(data.as_ref()) {
                            arbitrage_engine.update_orderbook_snapshot(&depth);
                        } else {
                            logln!("failed to parse market data");
                        }
                    }
                    WS_DEPTH_DF_T_1 => {
                        if let Some(depth_diff) = parse_futures_depth_update(data.as_ref()) {
                            arbitrage_engine.update_orderbook_diff(&depth_diff);
                        } else {
                            logln!("failed to parse market data");
                        }
                    }
                    WS_AGG_TRADE_T_1 => {
                        if let Some(trade) = parse_futures_agg_trade(data.as_ref()) {
                            info!("agg trade: {:?}", trade);
                        } else {
                            error!(
                                "failed to parse agg trade: {}",
                                String::from_utf8_lossy(data.as_ref())
                            );
                        }
                    }
                    TRADE_TOKEN_1 | TRADE_TOKEN_2 | TRADE_TOKEN_3 | TRADE_TOKEN_4 => {
                        if let Some(trade) = parse_sbe_trades(data.as_ref()) {
                            ArbitrageEngine::update_rate_by_trades(trade);
                        } else {
                            error!("failed to parse trade");
                        }
                    }
                    ORDER_TOKEN_1 => {
                        info!(
                            "recv order resp: {}",
                            String::from_utf8_lossy(data.as_ref())
                        );
                    }
                    USER_DATA_STREAM_1 => {
                        info!("recv user data: {}", String::from_utf8_lossy(data.as_ref()));
                        if !user_data_stream_subscribed {
                            handle.send_message(token, generate_futures_user_data_sub_request())?;
                            user_data_stream_subscribed = true;
                        }
                    }
                    _ => (),
                },
                Message::HttpResponse(response) => {
                    if let Some(body) = response.body.as_ref() {
                        update_trading_fee(body);
                        ArbitrageEngine::set_trading_fee_updated();
                        flush_logs!();
                    }
                }
                _ => (),
            },
            CallbackData::ConnectionOpen(token) => match token {
                ORDER_TOKEN_1 => {
                    info!("Order connection opened: {:?}", token);
                    let req = generate_futures_session_logon_request();
                    handle.send_message(token, req)?;
                }
                USER_DATA_STREAM_1 => {
                    info!("user data stream connected");
                    let req = generate_session_logon_request();
                    handle.send_message(token, req)?;
                }
                REST_TOKEN => {
                    if !ArbitrageEngine::get_trading_fee_updated() {
                        info!("quering trading fee");
                        ArbitrageEngine::set_trading_fee_updated();
                    }
                    // handle.send_message(REST_TOKEN, generate_trading_fee_request())?;
                }
                _ => (),
            },
            CallbackData::ConnectionClose(token, err) => {
                info!("connection close: {:?} {:?}", token, err);
                flush_logs!();
            }
            CallbackData::ConnectionError(token, error) => {
                error!("connection err: {:?}: {:?}", token, error);
                flush_logs!();
            }
        }
        Ok(())
    }
}

pub fn run() -> Result<()> {
    let mut settings = Settings::default();
    settings.event_loop_timeout = Some(std::time::Duration::from_millis(1));
    let mut websocket = WebSocket::new(settings, callback())?;

    let mut headers = std::collections::HashMap::new();
    headers.insert("X-MBX-APIKEY".to_string(), API_KEY.to_string());

    let bbo_url: Url = generate_futures_book_ticker_url(SYMBOL).into();
    info!("bbo url: {}", bbo_url);
    websocket.connect(bbo_url.clone(), WS_BBO_1)?;

    let depth_snapshot_url: Url = generate_futures_depth_snapshot_url(SYMBOL).into();
    info!("depth snapshot url: {}", depth_snapshot_url);
    websocket.connect(depth_snapshot_url.clone(), WS_DEPTH_SS_1)?;

    let depth_diff_url: Url = generate_futures_depth_diff_url(SYMBOL).into();
    info!("depth diff url: {}", depth_diff_url);
    websocket.connect(depth_diff_url.clone(), WS_DEPTH_DF_T_1)?;

    let agg_trade_url: Url = generate_futures_agg_trade_url(SYMBOL).into();
    info!("agg trade url: {}", agg_trade_url);
    websocket.connect(agg_trade_url.clone(), WS_AGG_TRADE_T_1)?;

    let order_url: Url = generate_futures_order_url().into();
    info!("order url: {}", order_url);
    websocket.connect(order_url.clone(), ORDER_TOKEN_1)?;

    let user_data_stream_url = "wss://ws-fapi.binance.com/ws-fapi/v1".to_string();
    info!("user data stream url: {}", user_data_stream_url);
    websocket.connect(user_data_stream_url, USER_DATA_STREAM_1)?;

    match websocket.run() {
        Ok(_) => (),
        Err(e) => {
            error!("Websocket run error: {:?}", e);
            flush_logs!();
        }
    }
    Ok(())
}
