pub fn generate_sbe_bbo_url(step: usize) -> String {
    let symbols = crate::engine::trading_pair::TradingPair::all_symbols();

    let streams: Vec<String> = symbols
        .iter()
        .filter(|symbol| {
            let index = symbols.iter().position(|s| s == *symbol).unwrap();
            index % 3 == step
        })
        .map(|symbol| format!("{}@bestBidAsk", symbol.to_lowercase()))
        .collect();

    format!(
        "wss://stream-sbe.binance.com:9443/stream?streams={}",
        streams.join("/")
    )
}

pub fn generate_futures_book_ticker_url(symbol: &str) -> String {
    format!(
        "wss://fstream.binance.com:443/stream?streams={}@bookTicker",
        symbol.to_lowercase()
    )
}

pub fn generate_futures_depth_snapshot_url(symbol: &str) -> String {
    format!(
        "wss://fstream.binance.com:443/stream?streams={}@depth20@100ms",
        symbol.to_lowercase()
    )
}

pub fn generate_futures_depth_diff_url(symbol: &str) -> String {
    format!(
        "wss://fstream.binance.com:443/stream?streams={}@depth@100ms",
        symbol.to_lowercase()
    )
}

pub fn generate_futures_agg_trade_url(symbol: &str) -> String {
    format!(
        "wss://fstream.binance.com:443/stream?streams={}@aggTrade",
        symbol.to_lowercase()
    )
}

pub fn generate_futures_order_url() -> String {
    "wss://ws-fapi.binance.com/ws-fapi/v1".to_string()
}

pub fn generate_ws_bbo_url(step: usize) -> String {
    let symbols = crate::engine::trading_pair::TradingPair::all_symbols();

    let streams: Vec<String> = symbols
        .iter()
        .filter(|symbol| {
            let index = symbols.iter().position(|s| s == *symbol).unwrap();
            index % 2 == step
        })
        .map(|symbol| format!("{}@bookTicker", symbol.to_lowercase()))
        .collect();

    format!(
        "wss://stream.binance.com:443/stream?streams={}",
        streams.join("/")
    )
}

pub fn generate_sbe_trade_url() -> String {
    let symbols = crate::engine::trading_pair::TradingPair::all_symbols();

    let streams: Vec<String> = symbols
        .iter()
        .map(|symbol| format!("{}@trade", symbol.to_lowercase()))
        .collect();

    format!(
        "wss://stream-sbe.binance.com:9443/stream?streams={}",
        streams.join("/")
    )
}

pub fn generate_sbe_depth_20_url(step: usize) -> String {
    let symbols = crate::engine::trading_pair::TradingPair::all_symbols();

    let streams: Vec<String> = symbols
        .iter()
        .filter(|symbol| {
            let index = symbols.iter().position(|s| s == *symbol).unwrap();
            index % 2 == step
        })
        .map(|symbol| format!("{}@depth20", symbol.to_lowercase()))
        .collect();

    format!(
        "wss://stream-sbe.binance.com:9443/stream?streams={}",
        streams.join("/")
    )
}

pub fn generate_ws_depth_5_url() -> String {
    let symbols = crate::engine::trading_pair::TradingPair::all_symbols();

    let streams: Vec<String> = symbols
        .iter()
        .map(|symbol| format!("{}@depth5@100ms", symbol.to_lowercase()))
        .collect();

    format!(
        "wss://stream.binance.com:9443/stream?streams={}",
        streams.join("/")
    )
}

pub fn generate_sbe_depth_diff_url() -> String {
    let symbols = crate::engine::trading_pair::TradingPair::all_symbols();

    let streams: Vec<String> = symbols
        .iter()
        .map(|symbol| format!("{}@depth", symbol.to_lowercase()))
        .collect();

    format!(
        "wss://stream-sbe.binance.com:9443/stream?streams={}",
        streams.join("/")
    )
}

pub fn generate_ws_depth_diff_url() -> String {
    let symbols = crate::engine::trading_pair::TradingPair::all_symbols();

    let streams: Vec<String> = symbols
        .iter()
        .map(|symbol| format!("{}@depth@100ms", symbol.to_lowercase()))
        .collect();

    format!(
        "wss://stream.binance.com:9443/stream?streams={}",
        streams.join("/")
    )
}

pub fn generate_market_data_ws_url_for_perf() -> String {
    let symbols = crate::engine::trading_pair::TradingPair::all_symbols();

    // SBE格式使用 bestBidAsk 流名称（等同于 JSON 的 bookTicker）
    let streams: Vec<String> = symbols
        .iter()
        .filter(|symbol| !symbol.contains("btc"))
        .map(|symbol| format!("{}@bestBidAsk", symbol.to_lowercase()))
        .collect();

    format!(
        "wss://stream-sbe.binance.com:9443/stream?streams={}",
        streams.join("/")
    )
}

pub fn generate_order_url() -> String {
    "wss://ws-api.binance.com:443/ws-api/v3".to_string()
}

pub fn generate_ws_agg_trades_url() -> String {
    let symbols = crate::engine::trading_pair::TradingPair::all_symbols();

    let streams: Vec<String> = symbols
        .iter()
        .map(|symbol| format!("{}@aggTrade", symbol.to_lowercase()))
        .collect();

    format!(
        "wss://stream.binance.com:9443/stream?streams={}",
        streams.join("/")
    )
}
