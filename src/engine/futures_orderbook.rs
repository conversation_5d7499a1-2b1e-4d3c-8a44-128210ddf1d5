//! Ultra‑low‑latency stack‑allocated Binance Futures order book implementation.
//!
//! * Completely allocation‑free after start‑up: no Vec, HashMap or heap at runtime.
//! * Suitable for `#![no_std]` environments (you can disable the few debug assertions).
//! * O(log N) search, O(N) insert/delete with `ptr::copy` memcpy‑style shifts.
//! * Best for depths up to a few thousand levels (N <= 5000) which covers Binance full book.
//! * Optimized for futures trading with liquidation price considerations.
//!
//! Adapted from Binance "How to manage a local order book correctly" steps — see developers.binance.com.
//!
//! Compile with: `cargo build --release` (requires Rust 1.77+ for const‑generics).

use core::cmp::Ordering;
use core::ptr;
use std::cmp::max;
use std::fmt::Display;

use crate::encoding::book_ticker::FuturesBookTicker;
use crate::encoding::depth_snapshot::FuturesDepthSnapshot;
use crate::encoding::depth_update::FuturesDepthUpdate;

/// A single futures order book level (price, quantity).
#[derive(<PERSON><PERSON>, Co<PERSON>, Debug, De<PERSON>ult)]
#[repr(C)]
pub struct FuturesLevel {
    pub price: f64,
    pub qty: f64,
}

/// A stack‑allocated futures side (bid or ask).
///
/// * `N` : maximum number of levels to keep.
/// * `IS_BID` : `true` for bids (sorted DESC), `false` for asks (ASC).
#[derive(Clone, Copy, Debug)]
pub struct FuturesSide<const N: usize, const IS_BID: bool> {
    levels: [FuturesLevel; N],
    len: usize,
}

impl<const N: usize, const IS_BID: bool> FuturesSide<N, IS_BID> {
    /// Empty side.
    #[inline]
    pub const fn new() -> Self {
        Self {
            levels: [FuturesLevel {
                price: 0.0,
                qty: 0.0,
            }; N],
            len: 0,
        }
    }

    /// Binary search for a price. Returns (found, index).
    #[inline(always)]
    fn find(&self, price: f64) -> (bool, usize) {
        let mut low = 0usize;
        let mut high = self.len;
        while low < high {
            let mid = (low + high) >> 1;
            let mid_price = unsafe { self.levels.get_unchecked(mid).price };
            let ord = if IS_BID {
                if price > mid_price {
                    Ordering::Less
                } else if price < mid_price {
                    Ordering::Greater
                } else {
                    Ordering::Equal
                }
            } else {
                if price < mid_price {
                    Ordering::Less
                } else if price > mid_price {
                    Ordering::Greater
                } else {
                    Ordering::Equal
                }
            };

            match ord {
                Ordering::Less => high = mid,
                Ordering::Greater => low = mid + 1,
                Ordering::Equal => return (true, mid),
            }
        }
        (false, low)
    }

    /// Find the nearest level to the target price. Returns (index, level).
    /// If the orderbook is empty, returns None.
    /// For bid side: if price > best_bid + 0.02%, returns None.
    /// For ask side: if price < best_ask - 0.02%, returns None.
    #[inline(always)]
    fn find_nearest(&self, price: f64) -> Option<(usize, &FuturesLevel)> {
        if self.len == 0 {
            return None;
        }

        // Check 0.02% threshold against the best price (1st level)
        let best_level = unsafe { self.levels.get_unchecked(0) };
        let best_price = best_level.price;
        let threshold = best_price * 0.0002; // 0.02% = 0.0002

        if IS_BID {
            // For bid side: reject if target price > best_bid + 0.02%
            if price > best_price + threshold {
                return None;
            }
        } else {
            // For ask side: reject if target price < best_ask - 0.02%
            if price < best_price - threshold {
                return None;
            }
        }

        let (found, idx) = self.find(price);

        if found {
            // Exact match found
            return Some((idx, unsafe { self.levels.get_unchecked(idx) }));
        }

        // No exact match, find the nearest level
        if idx == 0 {
            // Target price is before the first level
            Some((0, unsafe { self.levels.get_unchecked(0) }))
        } else if idx >= self.len {
            // Target price is after the last level
            let last_idx = self.len - 1;
            Some((last_idx, unsafe { self.levels.get_unchecked(last_idx) }))
        } else {
            // Target price is between two levels, find the closer one
            let prev_level = unsafe { self.levels.get_unchecked(idx - 1) };
            let next_level = unsafe { self.levels.get_unchecked(idx) };

            let prev_distance = (price - prev_level.price).abs();
            let next_distance = (price - next_level.price).abs();

            if prev_distance <= next_distance {
                Some((idx - 1, prev_level))
            } else {
                Some((idx, next_level))
            }
        }
    }

    /// Insert/update level. `qty == 0` removes.
    #[inline]
    pub fn upsert(&mut self, price: f64, qty: f64) {
        let (found, idx) = self.find(price);

        if found {
            if qty == 0.0 {
                // delete
                unsafe {
                    let dst = self.levels.as_mut_ptr().add(idx);
                    let src = dst.add(1);
                    ptr::copy(src, dst, self.len - idx - 1);
                }
                self.len -= 1;
            } else {
                unsafe {
                    self.levels.get_unchecked_mut(idx).qty = qty;
                }
            }
        } else if qty != 0.0 {
            // Check capacity to prevent array bounds overflow
            if self.len >= N {
                // For a 20-depth orderbook, discard levels beyond capacity
                // In a real trading system, you might want to:
                // 1. Replace the worst level if this is a better price
                // 2. Log a warning about dropped levels
                // 3. Implement a more sophisticated capacity management strategy
                return;
            }

            unsafe {
                let dst = self.levels.as_mut_ptr().add(idx + 1);
                let src = self.levels.as_ptr().add(idx);
                ptr::copy(src, dst, self.len - idx); // shift right
                let level = self.levels.get_unchecked_mut(idx);
                level.price = price;
                level.qty = qty;
            }
            self.len += 1;
        }
    }

    #[inline]
    pub fn iter(&self) -> impl Iterator<Item = &FuturesLevel> {
        self.levels[..self.len].iter()
    }
}

/// Complete futures order book (bids + asks) with monotonic update sequence tracking.
#[derive(Clone)]
pub struct FuturesOrderBook<const N: usize> {
    pub first_update_id: u64,
    pub final_update_id: u64,
    pub prev_final_update_id: u64,
    pub event_time: u64,
    pub transaction_time: u64,
    bids: FuturesSide<N, true>,
    asks: FuturesSide<N, false>,
    updated_by_snapshot: bool,
    pub book_ticker: Option<FuturesBookTicker>,
    pub best_bid: Option<FuturesLevel>,
    pub best_ask: Option<FuturesLevel>,
}

impl<const N: usize> FuturesOrderBook<N> {
    /// Empty book.
    pub const fn new() -> Self {
        Self {
            first_update_id: 0,
            final_update_id: 0,
            prev_final_update_id: 0,
            event_time: 0,
            transaction_time: 0,
            bids: FuturesSide::new(),
            asks: FuturesSide::new(),
            updated_by_snapshot: false,
            book_ticker: None,
            best_bid: None,
            best_ask: None,
        }
    }

    fn check_overlap(&self) {
        if self.bids.len > 0 && self.asks.len > 0 {
            if self.bids.levels[0].price >= self.asks.levels[0].price {
                panic!("Bids and asks overlap: {}", self);
            }
        }
    }

    /// Apply REST snapshot.
    pub fn apply_snapshot(&mut self, snapshot: &FuturesDepthSnapshot) -> bool {
        if snapshot.update_id <= self.final_update_id {
            return false;
        }
        self.final_update_id = snapshot.update_id;
        self.prev_final_update_id = self.final_update_id;
        self.first_update_id = snapshot.update_id;
        self.event_time = snapshot.event_time;
        self.transaction_time = snapshot.transaction_time;
        self.bids.len = 0;
        self.asks.len = 0;

        for &(p, q) in snapshot.bids.iter().take(N) {
            if p == 0.0 {
                break;
            }
            self.bids.upsert(p, q);
        }
        for &(p, q) in snapshot.asks.iter().take(N) {
            if p == 0.0 {
                break;
            }
            self.asks.upsert(p, q);
        }
        self.check_overlap();
        self.updated_by_snapshot = true;
        if let Some(book_ticker) = &self.book_ticker {
            if self.final_update_id > book_ticker.update_id {
                self.best_bid = Some(FuturesLevel {
                    price: book_ticker.bid_price,
                    qty: book_ticker.bid_qty,
                });
                self.best_ask = Some(FuturesLevel {
                    price: book_ticker.ask_price,
                    qty: book_ticker.ask_qty,
                });
            }
        }
        true
    }

    pub fn apply_diff(&mut self, diff: &FuturesDepthUpdate) -> bool {
        if diff.final_update_id < self.final_update_id {
            return false;
        }
        if diff.first_update_id > self.final_update_id {
            return false;
        }
        if !self.updated_by_snapshot && diff.prev_update_id != self.final_update_id {
            return false;
        }
        self.updated_by_snapshot = false;
        self.final_update_id = diff.final_update_id;
        self.prev_final_update_id = self.final_update_id;
        self.first_update_id = diff.first_update_id;
        self.event_time = diff.event_time;
        self.transaction_time = diff.transaction_time;
        for &(p, q) in diff.bid_updates.iter().take(N) {
            if p == 0.0 {
                break;
            }
            self.bids.upsert(p, q);
        }
        for &(p, q) in diff.ask_updates.iter().take(N) {
            if p == 0.0 {
                break;
            }
            self.asks.upsert(p, q);
        }

        self.check_overlap();
        if let Some(book_ticker) = &self.book_ticker {
            if self.final_update_id > book_ticker.update_id {
                self.best_bid = Some(FuturesLevel {
                    price: book_ticker.bid_price,
                    qty: book_ticker.bid_qty,
                });
                self.best_ask = Some(FuturesLevel {
                    price: book_ticker.ask_price,
                    qty: book_ticker.ask_qty,
                });
            }
        }
        true
    }

    pub fn apply_book_ticker(&mut self, book_ticker: &FuturesBookTicker) {
        if book_ticker.update_id <= self.final_update_id {
            return;
        }
        self.book_ticker = Some(book_ticker.clone());
        self.best_bid = Some(FuturesLevel {
            price: book_ticker.bid_price,
            qty: book_ticker.bid_qty,
        });
        self.best_ask = Some(FuturesLevel {
            price: book_ticker.ask_price,
            qty: book_ticker.ask_qty,
        });
    }

    #[inline]
    pub fn best_bid(&self) -> Option<&FuturesLevel> {
        self.best_bid.as_ref()
    }

    #[inline]
    pub fn best_ask(&self) -> Option<&FuturesLevel> {
        self.best_ask.as_ref()
    }

    #[inline]
    pub fn spread_in_bp(&self) -> f64 {
        if self.bids.len > 0 && self.asks.len > 0 {
            (self.asks.levels[0].price - self.bids.levels[0].price)
                / ((self.bids.levels[0].price + self.asks.levels[0].price) / 2.0)
                * 10000.0
        } else {
            0.0
        }
    }

    #[inline]
    pub fn find_ask_level(&self, price: f64) -> Option<(usize, &FuturesLevel)> {
        let (found, idx) = self.asks.find(price);
        if found {
            Some((idx, &self.asks.levels[idx]))
        } else {
            None
        }
    }

    #[inline]
    pub fn find_bid_level(&self, price: f64) -> Option<(usize, &FuturesLevel)> {
        let (found, idx) = self.bids.find(price);
        if found {
            Some((idx, &self.bids.levels[idx]))
        } else {
            None
        }
    }

    /// Find the nearest bid level to the target price.
    #[inline]
    pub fn find_nearest_bid_level(&self, price: f64) -> Option<(usize, &FuturesLevel)> {
        self.bids.find_nearest(price)
    }

    /// Find the nearest ask level to the target price.
    #[inline]
    pub fn find_nearest_ask_level(&self, price: f64) -> Option<(usize, &FuturesLevel)> {
        self.asks.find_nearest(price)
    }

    #[inline]
    pub fn asks_amount_at(&self, level: usize) -> f64 {
        if self.asks.len >= level + 1 {
            self.asks.levels[level].qty * self.asks.levels[level].price
        } else {
            0.0
        }
    }

    #[inline]
    pub fn bids_amount_at(&self, level: usize) -> f64 {
        if self.bids.len >= level + 1 {
            self.bids.levels[level].qty * self.bids.levels[level].price
        } else {
            0.0
        }
    }

    #[inline]
    pub fn bid_count(&self) -> usize {
        self.bids.len
    }

    #[inline]
    pub fn ask_count(&self) -> usize {
        self.asks.len
    }

    /// Calculate liquidation price based on leverage and position size
    #[inline]
    pub fn calculate_liquidation_price(
        &self,
        entry_price: f64,
        leverage: f64,
        position_size: f64,
        is_long: bool,
    ) -> f64 {
        if leverage <= 0.0 || position_size <= 0.0 {
            return 0.0;
        }

        let maintenance_margin = 0.005; // 0.5% maintenance margin (can be configurable)
        let liquidation_threshold = maintenance_margin / leverage;

        if is_long {
            entry_price * (1.0 - liquidation_threshold)
        } else {
            entry_price * (1.0 + liquidation_threshold)
        }
    }
}

impl<const N: usize> Display for FuturesOrderBook<N> {
    fn fmt(&self, f: &mut std::fmt::Formatter<'_>) -> std::fmt::Result {
        write!(f, "Futures Orderbook Snapshot: \n").unwrap();
        write!(
            f,
            "First Update ID: {} Final Update ID: {} Event Time: {} Transaction Time: {}\n",
            self.first_update_id, self.final_update_id, self.event_time, self.transaction_time
        )
        .unwrap();

        let max_len = max(self.bids.len, self.asks.len);
        for i in 0..max_len {
            if i < self.bids.len {
                let level = &self.bids.levels[i];
                write!(f, "    Bid: {}@{}\t\t", level.price, level.qty).unwrap();
            } else {
                write!(f, "\t\t\t\t").unwrap();
            }
            if i < self.asks.len {
                let level = &self.asks.levels[i];
                write!(f, "    Ask: {}@{}\n", level.price, level.qty).unwrap();
            } else {
                write!(f, "\t\t\t\t\n").unwrap();
            }
        }
        write!(f, "\n")
    }
}
