use crate::encoding::agg_trades::FuturesAggTrade;

/// 聚合后的交易数据，按秒聚合
#[derive(Debug, <PERSON><PERSON>, Copy)]
pub struct AggregatedTradeData {
    pub symbol: [u8; 16],     // 固定大小的symbol，支持最多16个字符
    pub symbol_len: u8,       // 实际symbol长度
    pub timestamp: u64,       // 秒级时间戳
    pub open: f64,            // 开盘价
    pub close: f64,           // 收盘价
    pub high: f64,            // 最高价
    pub low: f64,             // 最低价
    pub total_volume: f64,    // 总成交量
    pub sell_volume: f64,     // 卖出成交量
    pub buy_volume: f64,      // 买入成交量
    pub trade_count: u64,     // 交易次数
    pub last_trade_time: u64, // 最后交易时间
}

impl Default for AggregatedTradeData {
    fn default() -> Self {
        Self {
            symbol: [0u8; 16],
            symbol_len: 0,
            timestamp: 0,
            open: 0.0,
            close: 0.0,
            high: 0.0,
            low: 0.0,
            total_volume: 0.0,
            sell_volume: 0.0,
            buy_volume: 0.0,
            trade_count: 0,
            last_trade_time: 0,
        }
    }
}

impl AggregatedTradeData {
    /// 创建新的聚合数据
    pub fn new(symbol: &str, timestamp: u64, price: f64, trade_time: u64) -> Self {
        let mut symbol_bytes = [0u8; 16];
        let symbol_len = symbol.len().min(16);
        symbol_bytes[..symbol_len].copy_from_slice(&symbol.as_bytes()[..symbol_len]);

        Self {
            symbol: symbol_bytes,
            symbol_len: symbol_len as u8,
            timestamp,
            open: price,
            close: price,
            high: price,
            low: price,
            total_volume: 0.0,
            sell_volume: 0.0,
            buy_volume: 0.0,
            trade_count: 0,
            last_trade_time: trade_time,
        }
    }

    /// 获取symbol字符串
    pub fn get_symbol(&self) -> &str {
        std::str::from_utf8(&self.symbol[..self.symbol_len as usize]).unwrap_or("")
    }

    /// 更新价格和成交量
    pub fn update(
        &mut self,
        price: f64,
        quantity: f64,
        is_buyer_maker: bool,
        trade_count: u64,
        trade_time: u64,
    ) {
        self.close = price;
        self.high = self.high.max(price);
        self.low = self.low.min(price);
        self.total_volume += quantity;
        self.trade_count += trade_count;
        self.last_trade_time = self.last_trade_time.max(trade_time);

        if is_buyer_maker {
            self.sell_volume += quantity;
        } else {
            self.buy_volume += quantity;
        }
    }
}

/// 按symbol分组的聚合数据
#[derive(Debug, Clone)]
pub struct SymbolAggregations<const MAX_AGGS: usize> {
    pub symbol: [u8; 16],
    pub symbol_len: u8,
    pub aggregations: [Option<AggregatedTradeData>; MAX_AGGS],
    pub count: usize,
}

impl<const MAX_AGGS: usize> SymbolAggregations<MAX_AGGS> {
    /// 创建新的symbol聚合器
    pub fn new(symbol: &str) -> Self {
        let mut symbol_bytes = [0u8; 16];
        let symbol_len = symbol.len().min(16);
        symbol_bytes[..symbol_len].copy_from_slice(&symbol.as_bytes()[..symbol_len]);

        Self {
            symbol: symbol_bytes,
            symbol_len: symbol_len as u8,
            aggregations: std::array::from_fn(|_| None),
            count: 0,
        }
    }

    /// 获取symbol字符串
    pub fn get_symbol(&self) -> &str {
        std::str::from_utf8(&self.symbol[..self.symbol_len as usize]).unwrap_or("")
    }

    /// 添加或更新聚合数据
    pub fn add_or_update(
        &mut self,
        timestamp: u64,
        price: f64,
        quantity: f64,
        is_buyer_maker: bool,
        trade_count: u64,
        trade_time: u64,
    ) {
        // 查找是否已存在该时间戳的聚合数据
        let mut found_existing = false;
        for i in 0..self.count {
            if let Some(agg) = &mut self.aggregations[i] {
                if agg.timestamp == timestamp {
                    agg.update(price, quantity, is_buyer_maker, trade_count, trade_time);
                    found_existing = true;
                    break;
                }
            }
        }

        if found_existing {
            return;
        }

        // 如果不存在，创建新的聚合数据
        if self.count < MAX_AGGS {
            let symbol = self.get_symbol();
            let mut new_agg = AggregatedTradeData::new(symbol, timestamp, price, trade_time);
            new_agg.update(price, quantity, is_buyer_maker, trade_count, trade_time);
            self.aggregations[self.count] = Some(new_agg);
            self.count += 1;
        } else {
            // 如果已达到最大数量，替换最旧的数据
            let oldest_idx = self.find_oldest_timestamp();
            let symbol = self.get_symbol();
            let mut new_agg = AggregatedTradeData::new(symbol, timestamp, price, trade_time);
            new_agg.update(price, quantity, is_buyer_maker, trade_count, trade_time);
            self.aggregations[oldest_idx] = Some(new_agg);
        }
    }

    /// 查找最旧的时间戳索引
    fn find_oldest_timestamp(&self) -> usize {
        let mut oldest_idx = 0;
        let mut oldest_timestamp = u64::MAX;

        for i in 0..self.count {
            if let Some(agg) = &self.aggregations[i] {
                if agg.timestamp < oldest_timestamp {
                    oldest_timestamp = agg.timestamp;
                    oldest_idx = i;
                }
            }
        }
        oldest_idx
    }

    /// 获取所有聚合数据
    pub fn get_aggregations(&self) -> Vec<AggregatedTradeData> {
        let mut result = Vec::with_capacity(self.count);
        for i in 0..self.count {
            if let Some(agg) = self.aggregations[i] {
                result.push(agg);
            }
        }
        result.sort_by_key(|agg| agg.timestamp);
        result
    }

    /// 获取指定时间戳的聚合数据
    pub fn get_aggregation(&self, timestamp: u64) -> Option<&AggregatedTradeData> {
        for i in 0..self.count {
            if let Some(agg) = &self.aggregations[i] {
                if agg.timestamp == timestamp {
                    return Some(agg);
                }
            }
        }
        None
    }

    /// 清理过期的聚合数据
    pub fn cleanup_old_aggregations(&mut self, max_age_seconds: u64) {
        let current_time = std::time::SystemTime::now()
            .duration_since(std::time::UNIX_EPOCH)
            .unwrap()
            .as_secs();

        let mut i = 0;
        while i < self.count {
            if let Some(agg) = &self.aggregations[i] {
                if current_time - agg.timestamp > max_age_seconds {
                    // 移除过期的数据
                    self.aggregations[i] = None;
                    // 将最后一个非空数据移到当前位置
                    if self.count > 0 {
                        self.aggregations[i] = self.aggregations[self.count - 1];
                        self.aggregations[self.count - 1] = None;
                    }
                    self.count -= 1;
                } else {
                    i += 1;
                }
            } else {
                i += 1;
            }
        }
    }
}

/// 聚合器，用于将futures aggtrade数据按秒聚合
pub struct FuturesAggTradeAggregator<const MAX_AGGS: usize> {
    /// 按symbol分组的聚合数据
    symbol_aggregations: [Option<SymbolAggregations<MAX_AGGS>>; 100], // 支持最多100个symbol
    /// 实际symbol数量
    symbol_count: usize,
}

impl<const MAX_AGGS: usize> FuturesAggTradeAggregator<MAX_AGGS> {
    /// 创建新的聚合器
    pub fn new() -> Self {
        Self {
            symbol_aggregations: std::array::from_fn(|_| None),
            symbol_count: 0,
        }
    }

    /// 添加一个futures aggtrade到聚合器
    pub fn add_trade(&mut self, trade: &FuturesAggTrade) {
        // 使用event_time作为秒级时间戳（测试数据已经是秒级）
        let second_timestamp = trade.event_time;

        // 计算trade_count
        let trade_count = if trade.last_trade_id >= trade.first_trade_id {
            trade.last_trade_id - trade.first_trade_id + 1
        } else {
            1 // 防止溢出
        };

        // 查找或创建symbol的聚合数据
        let symbol_idx = self.find_or_create_symbol(&trade.symbol);
        if let Some(symbol_aggs) = &mut self.symbol_aggregations[symbol_idx] {
            symbol_aggs.add_or_update(
                second_timestamp,
                trade.price,
                trade.quantity,
                trade.is_buyer_maker,
                trade_count,
                trade.trade_time,
            );
        }
    }

    /// 查找或创建symbol的索引
    fn find_or_create_symbol(&mut self, symbol: &str) -> usize {
        // 查找已存在的symbol
        for i in 0..self.symbol_count {
            if let Some(symbol_aggs) = &self.symbol_aggregations[i] {
                if symbol_aggs.get_symbol() == symbol {
                    return i;
                }
            }
        }

        // 创建新的symbol聚合器
        if self.symbol_count < 100 {
            self.symbol_aggregations[self.symbol_count] = Some(SymbolAggregations::new(symbol));
            self.symbol_count += 1;
            self.symbol_count - 1
        } else {
            // 如果已达到最大symbol数量，返回第一个（可以改进为LRU策略）
            0
        }
    }

    /// 获取指定symbol的所有聚合数据
    pub fn get_aggregations(&self, symbol: &str) -> Vec<AggregatedTradeData> {
        for i in 0..self.symbol_count {
            if let Some(symbol_aggs) = &self.symbol_aggregations[i] {
                let stored_symbol = symbol_aggs.get_symbol();
                // 检查完全匹配或者symbol是存储symbol的前缀
                if stored_symbol == symbol
                    || symbol.starts_with(stored_symbol)
                    || stored_symbol.starts_with(symbol)
                {
                    return symbol_aggs.get_aggregations();
                }
            }
        }
        Vec::new()
    }

    /// 获取指定symbol和时间戳的聚合数据
    pub fn get_aggregation(&self, symbol: &str, timestamp: u64) -> Option<&AggregatedTradeData> {
        for i in 0..self.symbol_count {
            if let Some(symbol_aggs) = &self.symbol_aggregations[i] {
                if symbol_aggs.get_symbol() == symbol {
                    return symbol_aggs.get_aggregation(timestamp);
                }
            }
        }
        None
    }

    /// 获取所有symbol的聚合数据
    pub fn get_all_aggregations(&self) -> Vec<(String, Vec<AggregatedTradeData>)> {
        let mut result = Vec::with_capacity(self.symbol_count);
        for i in 0..self.symbol_count {
            if let Some(symbol_aggs) = &self.symbol_aggregations[i] {
                let symbol = symbol_aggs.get_symbol().to_string();
                let aggs = symbol_aggs.get_aggregations();
                result.push((symbol, aggs));
            }
        }
        result
    }

    /// 清空所有聚合数据
    pub fn clear(&mut self) {
        for i in 0..self.symbol_count {
            self.symbol_aggregations[i] = None;
        }
        self.symbol_count = 0;
    }

    /// 获取聚合器的统计信息
    pub fn get_stats(&self) -> (usize, usize) {
        let mut total_aggregations = 0;
        for i in 0..self.symbol_count {
            if let Some(symbol_aggs) = &self.symbol_aggregations[i] {
                total_aggregations += symbol_aggs.count;
            }
        }
        (self.symbol_count, total_aggregations)
    }

    /// 清理过期的聚合数据
    pub fn cleanup_old_aggregations(&mut self, max_age_seconds: u64) {
        for i in 0..self.symbol_count {
            if let Some(symbol_aggs) = &mut self.symbol_aggregations[i] {
                symbol_aggs.cleanup_old_aggregations(max_age_seconds);
            }
        }
    }
}

impl<const MAX_AGGS: usize> Default for FuturesAggTradeAggregator<MAX_AGGS> {
    fn default() -> Self {
        Self::new()
    }
}

#[cfg(test)]
mod tests {
    use super::*;
    use crate::encoding::agg_trades::FuturesAggTrade;

    #[test]
    fn test_aggregator_basic_functionality() {
        let mut aggregator = FuturesAggTradeAggregator::<10>::new();

        // 创建测试数据
        let trade1 = FuturesAggTrade {
            symbol: "BTCUSDT",
            event_time: 1000, // 1秒
            trade_time: 1000,
            price: 50000.0,
            quantity: 1.0,
            is_buyer_maker: false, // 买入
            first_trade_id: 100,
            last_trade_id: 100,
        };

        let trade2 = FuturesAggTrade {
            symbol: "BTCUSDT",
            event_time: 1000, // 同一秒内
            trade_time: 1999,
            price: 50100.0,
            quantity: 0.5,
            is_buyer_maker: true, // 卖出
            first_trade_id: 101,
            last_trade_id: 101,
        };

        // 添加交易
        aggregator.add_trade(&trade1);
        aggregator.add_trade(&trade2);

        // 验证聚合结果
        let aggs = aggregator.get_aggregations("BTCUSDT");
        assert_eq!(aggs.len(), 1);

        let agg = &aggs[0];
        assert_eq!(agg.get_symbol(), "BTCUSDT");
        assert_eq!(agg.timestamp, 1000); // 时间戳
        assert_eq!(agg.open, 50000.0);
        assert_eq!(agg.close, 50100.0);
        assert_eq!(agg.high, 50100.0);
        assert_eq!(agg.low, 50000.0);
        assert_eq!(agg.total_volume, 1.5);
        assert_eq!(agg.buy_volume, 1.0);
        assert_eq!(agg.sell_volume, 0.5);
        assert_eq!(agg.trade_count, 2);
        assert_eq!(agg.last_trade_time, 1999);
    }

    #[test]
    fn test_trade_count_calculation() {
        let mut aggregator = FuturesAggTradeAggregator::<10>::new();

        // 测试trade_count计算
        let trade = FuturesAggTrade {
            symbol: "BTCUSDT",
            event_time: 1000,
            trade_time: 1000,
            price: 50000.0,
            quantity: 1.0,
            is_buyer_maker: false,
            first_trade_id: 100,
            last_trade_id: 105, // 5个trade
        };

        aggregator.add_trade(&trade);

        let aggs = aggregator.get_aggregations("BTCUSDT");
        assert_eq!(aggs.len(), 1);
        assert_eq!(aggs[0].trade_count, 6); // 105 - 100 + 1 = 6
    }

    #[test]
    fn test_max_aggregations_limit() {
        let mut aggregator = FuturesAggTradeAggregator::<3>::new();

        // 添加4个不同秒的交易
        for i in 0..4 {
            let trade = FuturesAggTrade {
                symbol: "BTCUSDT",
                event_time: i as u64,
                trade_time: i as u64,
                price: 50000.0 + i as f64,
                quantity: 1.0,
                is_buyer_maker: false,
                first_trade_id: i as u64,
                last_trade_id: i as u64,
            };
            aggregator.add_trade(&trade);
        }

        // 应该只保留最新的3个
        let aggs = aggregator.get_aggregations("BTCUSDT");
        assert_eq!(aggs.len(), 3);

        // 验证保留的是最新的
        let timestamps: Vec<u64> = aggs.iter().map(|agg| agg.timestamp).collect();
        assert_eq!(timestamps, vec![1, 2, 3]); // 0被删除了
    }

    #[test]
    fn test_multiple_symbols() {
        let mut aggregator = FuturesAggTradeAggregator::<10>::new();

        // 添加不同symbol的交易
        let trade1 = FuturesAggTrade {
            symbol: "BTCUSDT",
            event_time: 1000,
            trade_time: 1000,
            price: 50000.0,
            quantity: 1.0,
            is_buyer_maker: false,
            first_trade_id: 100,
            last_trade_id: 100,
        };

        let trade2 = FuturesAggTrade {
            symbol: "ETHUSDT",
            event_time: 1000,
            trade_time: 1000,
            price: 3000.0,
            quantity: 2.0,
            is_buyer_maker: true,
            first_trade_id: 200,
            last_trade_id: 200,
        };

        aggregator.add_trade(&trade1);
        aggregator.add_trade(&trade2);

        let all_aggs = aggregator.get_all_aggregations();
        assert_eq!(all_aggs.len(), 2);
        assert_eq!(all_aggs[0].1.len(), 1);
        assert_eq!(all_aggs[1].1.len(), 1);
    }

    #[test]
    fn test_symbol_truncation() {
        let mut aggregator = FuturesAggTradeAggregator::<10>::new();

        // 测试超长symbol会被截断
        let long_symbol = "VERY_LONG_SYMBOL_NAME_THAT_EXCEEDS_16_CHARS";
        let trade = FuturesAggTrade {
            symbol: long_symbol,
            event_time: 1000,
            trade_time: 1000,
            price: 50000.0,
            quantity: 1.0,
            is_buyer_maker: false,
            first_trade_id: 100,
            last_trade_id: 100,
        };

        aggregator.add_trade(&trade);

        let aggs = aggregator.get_aggregations(long_symbol);
        assert_eq!(aggs.len(), 1);
        assert_eq!(aggs[0].get_symbol(), "VERY_LONG_SYMBOL");
        assert_eq!(aggs[0].symbol_len, 16);

        // 用截断后的symbol来查找
        let truncated_symbol = "VERY_LONG_SYMBOL";
        let aggs2 = aggregator.get_aggregations(truncated_symbol);
        assert_eq!(aggs2.len(), 1);
    }
}
