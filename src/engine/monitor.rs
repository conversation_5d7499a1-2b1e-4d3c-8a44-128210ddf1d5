use core::f64;

use crate::{
    Currency, EdgeDirection, PREDEFINED_RINGS, RING_FILLED_ORDERS, TRADING_PAIR_RATES, TradingPair,
    WebSocketHandle,
    encoding::order_update::{OrderResponse, parse_order_update},
    engine::{
        arbitrage_engine::{
            ArbitrageEngine, EXPECT_RATE, ORDER_PRICES, PENDING_ORDERS, PENDING_ORDERS_LEN,
        },
        token::{ORDER_TOKEN_1, ORDER_TOKEN_2, ORDER_TOKEN_3, ORDER_TOKEN_4},
        trade::generate_gtc_order,
        trading_pair::{CURRENCY_BALANCES, RING_FILLED_PRICES, RING_ORDER_STATUS},
    },
    error, error_unsafe, flush_logs, info_unsafe, trace,
    utils::{
        self,
        perf::{circles_to_ns, system_now_in_ms},
    },
};

#[derive(Debug)]
pub struct LatencyStats {
    order_latencies: [f64; 100],
    order_index: usize,
    pub win_count: usize,
    pub loss_count: usize,
}

impl LatencyStats {
    pub fn new() -> Self {
        Self {
            order_latencies: [0.0; 100],
            order_index: 0,
            win_count: 1,
            loss_count: 1,
        }
    }

    #[inline(always)]
    pub fn add_arbitrage_latency(&mut self, latency_ns: f64) {
        self.order_latencies[self.order_index] = latency_ns;
        self.order_index += 1;
    }

    pub fn print_stats(&mut self) {
        error!(
            "Wins: {} Losses: {}",
            self.win_count - 1,
            self.loss_count - 1
        );
    }
}

// 全局延迟统计
static mut LATENCY_STATS: Option<LatencyStats> = None;

pub fn init_latency_stats() {
    unsafe {
        LATENCY_STATS = Some(LatencyStats::new());
    }
}

#[inline(always)]
pub fn add_arbitrage_latency(latency_ns: f64) {
    unsafe {
        // 单线程环境，直接访问，无需任何同步开销
        if let Some(ref mut stats) = LATENCY_STATS {
            stats.add_arbitrage_latency(latency_ns);
        }
    }
}

pub fn print_latency_stats() {
    unsafe {
        if let Some(ref mut stats) = LATENCY_STATS {
            stats.print_stats();
        }
    }
}

pub static mut CURRENT_RING_TS: u64 = 0;
static mut PNL: f64 = 0.0;

pub fn check_complete(ring_index: usize) {
    unsafe {
        let ring_len = PREDEFINED_RINGS[ring_index].len();
        if RING_FILLED_ORDERS[ring_index][1] == RING_FILLED_ORDERS[ring_index][0] {
            RING_FILLED_ORDERS[ring_index][1] = 0;
            let maker_count = RING_FILLED_ORDERS[ring_index][2] as f64;
            let taker_count = ring_len - maker_count as usize;
            let expect: f64 = 1.0 + taker_count as f64 * 0.000175 + maker_count as f64 * 0.0000875;
            RING_FILLED_ORDERS[ring_index][2] = 0;
            let mut real = 1.0f64;
            let mut status_sum = 0;
            for i in 0..ring_len {
                real *= RING_FILLED_PRICES[ring_index][i];
                status_sum += RING_ORDER_STATUS[ring_index][i] as usize;
            }
            if status_sum == ring_len {
                error_unsafe!("Real: {:.6} vs Expect: {:.6}", real, expect);
                if (real - expect) < -0.0000001 {
                    PNL -= (expect - real) * 15.0;
                    if let Some(ref mut stats) = LATENCY_STATS {
                        stats.loss_count += 1;
                        error_unsafe!("Loss!");
                        stats.print_stats();
                    }
                } else {
                    PNL += (real - expect) * 15.0;
                    if let Some(ref mut stats) = LATENCY_STATS {
                        stats.win_count += 1;
                        error_unsafe!("Win!");
                        stats.print_stats();
                    }
                }
            } else {
                PNL -= 0.000175 * 2.0 * 15.0;
                if let Some(ref mut stats) = LATENCY_STATS {
                    stats.loss_count += 1;
                    error_unsafe!("Loss!");
                    stats.print_stats();
                }
            }
            let pnl = PNL;
            error_unsafe!("PNL: {}", pnl);
            flush_logs!();
            RING_ORDER_STATUS[ring_index] = [0; 4];
        }
    }
}

pub fn monitor_order_execution<const IN_LEN: usize, const OUT_LEN: usize>(
    _edge_index: usize,
    msg: &[u8],
    handle: &mut WebSocketHandle<IN_LEN, OUT_LEN>,
) -> Option<usize> {
    if let Some(order_res) = parse_order_update(msg) {
        let now = utils::perf::now();
        match order_res {
            OrderResponse::OrderUpdate(ou) => {
                let ring_index = ou.order_id.ring_index;
                let edge_index = ou.order_id.edge_index;
                let order_create_time = ou.order_id.order_create_time;
                let is_testing = ou.order_id.is_testing;
                match ou.status.as_str() {
                    "NEW" if is_testing => {
                        let latency_us = circles_to_ns(now - order_create_time) / 1000.0;
                        if latency_us > 2300.0 {
                            trace!(
                                "Slow order connection: {} latency: {}",
                                ou.symbol, latency_us
                            );
                            return Some(5);
                        }
                        None
                    }
                    "EXPIRED" if !is_testing => {
                        let latency = circles_to_ns(now - order_create_time) / 1000.0;
                        error!(
                            "{} {} p: {} t: {} l: {}",
                            ou.status.as_str(),
                            ou.symbol,
                            ou.fill_price,
                            ou.order_trasaction_time,
                            latency,
                        );
                        unsafe {
                            let current_ring_ts = CURRENT_RING_TS;
                            if order_create_time != current_ring_ts {
                                error_unsafe!(
                                    "order expired after next arb start {}, order_create_time: {} current_ring_ts: {}",
                                    ou.symbol,
                                    order_create_time,
                                    current_ring_ts
                                );
                                RING_ORDER_STATUS[ring_index][edge_index] = 2;
                                RING_FILLED_ORDERS[ring_index][1] += 1;
                                error_unsafe!(
                                    "Order filled count: {} {} {}",
                                    ring_index,
                                    RING_FILLED_ORDERS[ring_index][1],
                                    ou.symbol,
                                );
                                check_complete(ring_index);
                                return None;
                            }
                            let price = ORDER_PRICES[edge_index];
                            let edge = PREDEFINED_RINGS[ring_index][edge_index];
                            let adjusted_expect_rate = if EXPECT_RATE > 1.001 {
                                1.001
                            } else {
                                EXPECT_RATE
                            };
                            let new_price = match edge.1 {
                                EdgeDirection::Forward => price * (adjusted_expect_rate - 0.0005),
                                EdgeDirection::Reverse => price * (2.0005 - adjusted_expect_rate),
                            };
                            let timestamp = system_now_in_ms();
                            let token = match edge_index {
                                0 => ORDER_TOKEN_1,
                                1 => ORDER_TOKEN_2,
                                2 => ORDER_TOKEN_3,
                                3 => ORDER_TOKEN_4,
                                _ => panic!("Invalid edge index: {}", edge_index),
                            };
                            let buf = handle.get_write_buf(token).unwrap();
                            generate_gtc_order(
                                ring_index,
                                CURRENT_RING_TS,
                                timestamp,
                                buf,
                                edge_index,
                                new_price,
                            );
                            RING_ORDER_STATUS[ring_index][edge_index] = 3;
                            ArbitrageEngine::record_pending_order(
                                CURRENT_RING_TS,
                                timestamp,
                                ring_index,
                                edge_index,
                            );
                            handle.trigger_write(token).unwrap();
                            info_unsafe!(
                                "expired order to GTC order: {} {} new price: {} old price: {}",
                                ou.symbol,
                                edge.1.to_str(),
                                new_price,
                                price
                            );
                        }
                        None
                    }
                    "REJECTED" | "CANCELED" if !is_testing => {
                        unsafe {
                            error_unsafe!(
                                "{} {}",
                                ou.status.as_str(),
                                PREDEFINED_RINGS[ring_index][edge_index].0.to_str(),
                            );
                            RING_ORDER_STATUS[ring_index][edge_index] = 2;
                            RING_FILLED_ORDERS[ring_index][1] += 1;
                            error_unsafe!(
                                "Order filled count: {} {}",
                                ring_index,
                                RING_FILLED_ORDERS[ring_index][1]
                            );
                            for i in 0..PENDING_ORDERS_LEN {
                                if PENDING_ORDERS[i].0 == order_create_time {
                                    PENDING_ORDERS[i] = (0, 0, 0, 0);
                                }
                            }
                            check_complete(ring_index);
                        }
                        None
                    }
                    "TRADE" if !is_testing => unsafe {
                        if ou.remaining_quantity < 0.00000000001 {
                            if ou.is_maker {
                                RING_FILLED_ORDERS[ring_index][2] += 1;
                            }
                            RING_FILLED_ORDERS[ring_index][1] += 1;
                            RING_ORDER_STATUS[ring_index][edge_index] = 1;
                            ArbitrageEngine::cancel_pending_order(order_create_time);
                            error_unsafe!(
                                "Order filled count: {} {}",
                                ring_index,
                                RING_FILLED_ORDERS[ring_index][1]
                            );
                        }
                        match PREDEFINED_RINGS[ring_index][edge_index].1 {
                            EdgeDirection::Forward => {
                                RING_FILLED_PRICES[ring_index][edge_index] = 1.0 / ou.fill_price;
                            }
                            EdgeDirection::Reverse => {
                                RING_FILLED_PRICES[ring_index][edge_index] = ou.fill_price;
                            }
                        }
                        let latency = circles_to_ns(now - order_create_time) / 1000.0;
                        error_unsafe!(
                            "{} {} p: {} t: {} l: {:.1} is_maker: {}",
                            ou.status.as_str(),
                            PREDEFINED_RINGS[ring_index][edge_index].0.to_str(),
                            ou.fill_price,
                            ou.order_trasaction_time,
                            latency,
                            ou.is_maker,
                        );
                        check_complete(ring_index);
                        None
                    },
                    _ => None,
                }
            }
            OrderResponse::OrderCreated(_ou) => None,
            OrderResponse::AccountPosition(account_pos) => unsafe {
                for balance in account_pos.balances {
                    let price = match balance.asset {
                        Currency::XUSDT => 1.0,
                        _ => {
                            let pair = TradingPair::from((balance.asset, Currency::XUSDT));
                            TRADING_PAIR_RATES[pair as usize][EdgeDirection::Reverse as usize]
                        }
                    };
                    CURRENCY_BALANCES[balance.asset as usize] = balance.free * price;
                }
                None
            },
            OrderResponse::OrderError(err) => {
                if err.order_id.order_create_time == 0 || err.order_id.ring_index == 0 {
                    return None;
                }
                unsafe {
                    let ring_index = err.order_id.ring_index;
                    let edge_index = err.order_id.edge_index;
                    error_unsafe!(
                        "ERROR {} {}",
                        PREDEFINED_RINGS[ring_index][edge_index].0.to_str(),
                        err.error_message,
                    );
                    flush_logs!();
                    RING_ORDER_STATUS[ring_index][edge_index] = 2;
                    RING_FILLED_ORDERS[ring_index][1] += 1;
                    ArbitrageEngine::cancel_pending_order(err.order_id.order_create_time);
                    check_complete(ring_index);
                }
                None
            }
        }
    } else {
        let msg_str = String::from_utf8_lossy(msg);
        if msg_str.contains("200") || msg_str.contains("executionReport") {
            return None;
        }
        error!("parse order update err: {:?}", msg_str);
        flush_logs!();
        None
    }
}
