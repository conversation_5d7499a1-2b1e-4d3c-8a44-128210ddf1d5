use std::collections::HashMap;

use mio::Token;

use crate::{
    CallbackData, Message, Result, Settings, WebSocket, WebSocketHandle,
    engine::{
        trade::{generate_all_open_orders_request, generate_cancel_order_request},
        trading_pair::API_KEY,
    },
    error, flush_logs, info,
    net::utils::url::Url,
};

pub fn cancel_orders() {
    const OPEN_ORDERS_TOKEN: Token = Token(99);
    const CANCEL_ORDERS_TOKEN: Token = Token(98);
    const IN_LEN: usize = 1024 * 32;
    const OUT_LEN: usize = 1024 * 4;
    let mut cancel_order_connected = false;
    let mut open_order_connected = false;
    let mut cancel_orders = HashMap::<String, u64>::new();
    let callback = move |handle: &mut WebSocketHandle<IN_LEN, OUT_LEN>,
                         cd: CallbackData|
          -> Result<()> {
        match cd {
            CallbackData::Message(token, msg) => match token {
                OPEN_ORDERS_TOKEN => {
                    if let Message::HttpResponse(data) = msg {
                        flush_logs!();
                        if let Some(body) = data.body.as_ref() {
                            let json: serde_json::Value =
                                serde_json::from_slice(body.as_ref()).expect("Invalid JSON format");
                            if let Some(orders) = json.as_array() {
                                for order in orders {
                                    info!(
                                        "got open order: {:?} {:?}",
                                        order.get("symbol"),
                                        order.get("orderId")
                                    );
                                    let sym = order
                                        .get("symbol")
                                        .and_then(|v| v.as_str())
                                        .map(str::trim)
                                        .unwrap();
                                    cancel_orders.insert(
                                        sym.to_string(),
                                        order.get("orderId").unwrap().to_string().parse().unwrap(),
                                    );
                                }
                                if cancel_orders.len() > 0 {
                                    if cancel_order_connected {
                                        let req = generate_cancel_order_request(
                                            cancel_orders.keys().next().unwrap(),
                                        );
                                        info!("sending cancel order request: {:?}", req);
                                        handle.send_message(
                                            CANCEL_ORDERS_TOKEN,
                                            generate_cancel_order_request(
                                                cancel_orders.keys().next().unwrap(),
                                            ),
                                        )?;
                                        info!("sent cancel order request");
                                        flush_logs!();
                                    } else {
                                        info!("waiting for cancel order connection");
                                        flush_logs!();
                                    }
                                } else {
                                    info!("no open orders to cancel");
                                    flush_logs!();
                                    handle.stop();
                                }
                            } else {
                                panic!("got open orders message: {:?}", json);
                            }
                        } else {
                            error!("got open orders message: {:?}", data);
                            flush_logs!();
                        }
                    } else {
                        error!("got open orders message: {:?}", msg);
                        flush_logs!();
                    }
                }
                CANCEL_ORDERS_TOKEN => {
                    if let Message::HttpResponse(data) = msg {
                        if let Some(body) = data.body.as_ref() {
                            let json: serde_json::Value =
                                serde_json::from_slice(body.as_ref()).expect("Invalid JSON format");
                            if let Some(orders) = json.as_array() {
                                for order in orders {
                                    let symbol = order
                                        .get("symbol")
                                        .and_then(|v| v.as_str())
                                        .map(str::trim)
                                        .unwrap();
                                    cancel_orders.remove(symbol);
                                    info!("cancelled order: {:?}", symbol);
                                    flush_logs!();
                                }
                                if cancel_orders.len() > 0 {
                                    handle.send_message(
                                        token,
                                        generate_cancel_order_request(
                                            cancel_orders.keys().next().unwrap(),
                                        ),
                                    )?;
                                } else {
                                    info!("no open orders to cancel");
                                    flush_logs!();
                                    handle.stop();
                                }
                            } else {
                                panic!("got cancel orders message: {:?}", json);
                            }
                        } else {
                            error!("got cancel orders message: {:?}", data);
                            flush_logs!();
                            handle.stop();
                        }
                    } else {
                        panic!("got cancel orders message: {:?}", msg);
                    }
                }
                _ => {}
            },
            CallbackData::ConnectionOpen(token) => match token {
                OPEN_ORDERS_TOKEN => {
                    info!("getting all open orders connected");
                    flush_logs!();
                    open_order_connected = true;
                    let req = generate_all_open_orders_request();
                    handle.send_message(token, req)?;
                }
                CANCEL_ORDERS_TOKEN => {
                    info!("cancelling all orders connected");
                    flush_logs!();
                    cancel_order_connected = true;
                    if cancel_orders.len() > 0 && open_order_connected {
                        let req =
                            generate_cancel_order_request(cancel_orders.keys().next().unwrap());
                        handle.send_message(token, req)?;
                    }
                }
                _ => {}
            },
            CallbackData::ConnectionError(token, e) => {
                error!("Connection error: {:?} {:?}", token, e);
                flush_logs!();
                handle.stop();
            }
            _ => {
                info!("got other callback: {:?}", cd);
                flush_logs!();
                handle.stop();
            }
        }
        Ok(())
    };
    let settings = Settings::default();
    let mut websocket = WebSocket::new(settings, callback).unwrap();

    let mut headers = std::collections::HashMap::new();
    headers.insert("X-MBX-APIKEY".to_string(), API_KEY.to_string());

    let url: Url = "https://api.binance.com/".into();

    websocket
        .connect_with_headers(url.clone(), OPEN_ORDERS_TOKEN, headers.clone())
        .unwrap();
    websocket
        .connect_with_headers(url.clone(), CANCEL_ORDERS_TOKEN, headers.clone())
        .unwrap();

    match websocket.run() {
        Ok(_) => {
            info!("cancel orders finished");
            flush_logs!();
        }
        Err(e) => {
            error!("websocket run error: {:?}", e);
            flush_logs!();
        }
    }
    flush_logs!();
}
