use memchr;
use std::str;

// {
//   "e": "depthUpdate", // Event type
//   "E": 1571889248277, // Event time
//   "T": 1571889248276, // Transaction time
//   "s": "BTCUSDT",
//   "U": 390497796,     // First update ID in event
//   "u": 390497878,     // Final update ID in event
//   "pu": 390497794,    // Final update Id in last stream(ie `u` in last stream)
//   "b": [              // Bids to be updated
//     [
//       "7403.89",      // Price Level to be updated
//       "0.002"         // Quantity
//     ],
//     [
//       "7403.90",
//       "3.906"
//     ],
//     [
//       "7404.00",
//       "1.428"
//     ],
//     [
//       "7404.85",
//       "5.239"
//     ],
//     [
//       "7405.43",
//       "2.562"
//     ]
//   ],
//   "a": [              // Asks to be updated
//     [
//       "7405.96",      // Price level to be
//       "3.340"         // Quantity
//     ],
//     [
//       "7406.63",
//       "4.525"
//     ],
//     [
//       "7407.08",
//       "2.475"
//     ],
//     [
//       "7407.15",
//       "4.800"
//     ],
//     [
//       "7407.20",
//       "0.175"
//     ]
//   ]
// }
#[derive(Debug)]
pub struct FuturesDepthSnapshot<'a> {
    pub event_time: u64,
    pub transaction_time: u64,
    pub update_id: u64,
    pub symbol: &'a str,
    pub bids: [(f64, f64); 20],
    pub asks: [(f64, f64); 20],
}

// 辅助函数：解析深度数组
fn parse_depth_array(input: &[u8]) -> Option<[(f64, f64); 20]> {
    let mut result = [(0.0, 0.0); 20];
    let mut count = 0;

    // 将字节数组转换为字符串
    let input_str = match str::from_utf8(input) {
        Ok(s) => s,
        Err(_) => return None,
    };

    // 查找数组开始和结束位置
    let start = input_str.find('[')?;
    let end = input_str.rfind(']')?;
    let array_content = &input_str[start + 1..end];

    // 分割数组元素
    let elements: Vec<&str> = array_content
        .split("],[")
        .map(|s| s.trim_matches('[').trim_matches(']'))
        .collect();

    for element in elements.iter().take(20) {
        let parts: Vec<&str> = element.split(',').collect();
        if parts.len() >= 2 {
            let price = parts[0].trim_matches('"').parse::<f64>().unwrap_or(0.0);
            let qty = parts[1].trim_matches('"').parse::<f64>().unwrap_or(0.0);
            result[count] = (price, qty);
            count += 1;
        }
    }

    Some(result)
}

pub fn parse_futures_depth_snapshot(input: &[u8]) -> Option<FuturesDepthSnapshot<'_>> {
    let event_time_pattern = b"\"E\":";
    let start = memchr::memmem::find(input, event_time_pattern)?;
    let start = start + event_time_pattern.len();
    let end = memchr::memchr(b',', &input[start..])?;
    let event_time_str = &input[start..start + end];

    let transaction_time_pattern = b"\"T\":";
    let start = memchr::memmem::find(input, transaction_time_pattern)?;
    let start = start + transaction_time_pattern.len();
    let end = memchr::memchr(b',', &input[start..])?;
    let transaction_time_str = &input[start..start + end];

    let update_id_pattern = b"\"u\":";
    let start = memchr::memmem::find(input, update_id_pattern)?;
    let start = start + update_id_pattern.len();
    let end = memchr::memchr(b',', &input[start..])?;
    let update_id_str = &input[start..start + end];

    let symbol_pattern = b"\"s\":\"";
    let start = memchr::memmem::find(input, symbol_pattern)?;
    let start = start + symbol_pattern.len();
    let end = memchr::memchr(b'"', &input[start..])?;
    let symbol = &input[start..start + end];

    let bids_pattern = b"\"b\":[";
    let start = memchr::memmem::find(input, bids_pattern)?;
    let bids_start = start + bids_pattern.len();

    // 找到匹配的数组结束括号
    let mut bracket_count = 1; // 从1开始，因为我们已经找到了开始的[
    let mut end = bids_start;
    for (i, &byte) in input[bids_start..].iter().enumerate() {
        if byte == b'[' {
            bracket_count += 1;
        } else if byte == b']' {
            bracket_count -= 1;
            if bracket_count == 0 {
                end = bids_start + i;
                break;
            }
        }
    }
    let bids_section = &input[bids_start..end];

    let asks_pattern = b"\"a\":[";
    let start = memchr::memmem::find(input, asks_pattern)?;
    let asks_start = start + asks_pattern.len();

    // 找到匹配的数组结束括号
    let mut bracket_count = 1; // 从1开始，因为我们已经找到了开始的[
    let mut end = asks_start;
    for (i, &byte) in input[asks_start..].iter().enumerate() {
        if byte == b'[' {
            bracket_count += 1;
        } else if byte == b']' {
            bracket_count -= 1;
            if bracket_count == 0 {
                end = asks_start + i;
                break;
            }
        }
    }
    let asks_section = &input[asks_start..end];

    // 解析深度数组
    let bids = parse_depth_array(bids_section).unwrap_or([(0.0, 0.0); 20]);
    let asks = parse_depth_array(asks_section).unwrap_or([(0.0, 0.0); 20]);

    // 转换字符串为数字
    let event_time = unsafe { str::from_utf8_unchecked(event_time_str) }
        .parse::<u64>()
        .unwrap_or(0);
    let transaction_time = unsafe { str::from_utf8_unchecked(transaction_time_str) }
        .parse::<u64>()
        .unwrap_or(0);
    let update_id = unsafe { str::from_utf8_unchecked(update_id_str) }
        .parse::<u64>()
        .unwrap_or(0);
    let symbol = unsafe { str::from_utf8_unchecked(symbol) };

    Some(FuturesDepthSnapshot {
        event_time,
        transaction_time,
        update_id,
        symbol,
        bids,
        asks,
    })
}

#[derive(Debug)]
pub struct DepthSnapshot<'a> {
    pub last_update_id: u64,
    pub symbol: &'a str,
    pub bids: [(f64, f64); 1],
    pub asks: [(f64, f64); 1],
}

pub fn parse_depth_snapshot(input: &[u8]) -> Option<DepthSnapshot<'_>> {
    let update_id_pattern = b"\"eId\":";
    let start = memchr::memmem::find(input, update_id_pattern)?;
    let start = start + update_id_pattern.len();
    let end = memchr::memchr(b',', &input[start..])?;
    let update_id_str = &input[start..start + end];

    let symbol_pattern = b"eam\":\"";
    let start = memchr::memmem::find(input, symbol_pattern)?;
    let start = start + symbol_pattern.len();
    let end = memchr::memchr(b'@', &input[start..])?;
    let symbol = &input[start..start + end];

    // 3. 解析 bids 数组
    let bids_pattern = b"\"ds\":[[\"";
    let start = memchr::memmem::find(input, bids_pattern)?;
    let start = start + bids_pattern.len();
    let end = memchr::memchr(b'"', &input[start..])?;
    let bid1_price = &input[start..start + end];

    // 4. 解析 asks 数组
    let asks_pattern = b"\"ks\":[[\"";
    let start = memchr::memmem::find(input, asks_pattern)?;
    let start = start + asks_pattern.len();
    let end = memchr::memchr(b'"', &input[start..])?;
    let ask1_price = &input[start..start + end];

    let update_id = unsafe { std::mem::transmute::<&[u8], &str>(update_id_str) };
    let symbol = unsafe { std::mem::transmute::<&[u8], &str>(symbol) };
    let bid1_price = unsafe { std::mem::transmute::<&[u8], &str>(bid1_price) };
    let ask1_price = unsafe { std::mem::transmute::<&[u8], &str>(ask1_price) };

    Some(DepthSnapshot {
        last_update_id: update_id.parse().unwrap(),
        symbol,
        bids: [(bid1_price.parse().unwrap(), 0.0); 1],
        asks: [(ask1_price.parse().unwrap(), 0.0); 1],
    })
}

#[cfg(test)]
mod tests {
    use super::*;

    #[test]
    fn test_parse_futures_depth_snapshot() {
        let test_data = r#"{"e":"depthUpdate","E":1571889248277,"T":1571889248276,"s":"BTCUSDT","U":390497796,"u":390497878,"pu":390497794,"b":[["7403.89","0.002"],["7403.90","3.906"],["7404.00","1.428"]],"a":[["7405.96","3.340"],["7406.63","4.525"],["7407.08","2.475"]]}"#;

        let result = parse_futures_depth_snapshot(test_data.as_bytes());
        assert!(result.is_some());

        let snapshot = result.unwrap();
        assert_eq!(snapshot.event_time, 1571889248277);
        assert_eq!(snapshot.transaction_time, 1571889248276);
        assert_eq!(snapshot.update_id, 390497878);
        assert_eq!(snapshot.symbol, "BTCUSDT");

        // 检查 bids
        assert_eq!(snapshot.bids[0], (7403.89, 0.002));
        assert_eq!(snapshot.bids[1], (7403.90, 3.906));
        assert_eq!(snapshot.bids[2], (7404.00, 1.428));

        // 检查 asks
        assert_eq!(snapshot.asks[0], (7405.96, 3.340));
        assert_eq!(snapshot.asks[1], (7406.63, 4.525));
        assert_eq!(snapshot.asks[2], (7407.08, 2.475));

        // 检查未填充的档位应该为 (0.0, 0.0)
        assert_eq!(snapshot.bids[3], (0.0, 0.0));
        assert_eq!(snapshot.asks[19], (0.0, 0.0));
    }

    #[test]
    fn test_parse_depth_array() {
        let test_bids = r#"[["7403.89","0.002"],["7403.90","3.906"],["7404.00","1.428"]]"#;
        let result = parse_depth_array(test_bids.as_bytes());
        assert!(result.is_some());

        let bids = result.unwrap();
        assert_eq!(bids[0], (7403.89, 0.002));
        assert_eq!(bids[1], (7403.90, 3.906));
        assert_eq!(bids[2], (7404.00, 1.428));
        assert_eq!(bids[3], (0.0, 0.0)); // 未填充的档位
    }
}
