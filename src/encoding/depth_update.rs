use memchr;
use std::str::from_utf8_unchecked;

// {
//   "e": "depthUpdate", // Event type
//   "E": 123456789,     // Event time
//   "T": 123456788,     // Transaction time
//   "s": "BTCUSDT",     // Symbol
//   "U": 157,           // First update ID in event
//   "u": 160,           // Final update ID in event
//   "pu": 149,          // Final update Id in last stream(ie `u` in last stream)
//   "b": [              // Bids to be updated
//     [
//       "0.0024",       // Price level to be updated
//       "10"            // Quantity
//     ]
//   ],
//   "a": [              // Asks to be updated
//     [
//       "0.0026",       // Price level to be updated
//       "100"          // Quantity
//     ]
//   ]
// }
#[derive(Debug)]
pub struct FuturesDepthUpdate<'a> {
    pub symbol: &'a str,
    pub event_time: u64,
    pub transaction_time: u64,
    pub first_update_id: u64,
    pub final_update_id: u64,
    pub prev_update_id: u64,
    pub bid_updates: Vec<(f64, f64)>,
    pub ask_updates: Vec<(f64, f64)>,
}

// 辅助函数：解析价格档位更新数组
fn parse_price_levels_update(data: &[u8]) -> Vec<(f64, f64)> {
    let mut result = Vec::new();

    // 将字节数组转换为字符串
    let data_str = match std::str::from_utf8(data) {
        Ok(s) => s,
        Err(_) => return result,
    };

    // 查找数组开始和结束位置
    let start = match data_str.find('[') {
        Some(pos) => pos,
        None => return result,
    };
    let end = match data_str.rfind(']') {
        Some(pos) => pos,
        None => return result,
    };
    let array_content = &data_str[start + 1..end];

    // 分割数组元素
    let elements: Vec<&str> = array_content
        .split("],[")
        .map(|s| s.trim_matches('[').trim_matches(']'))
        .collect();

    for element in elements {
        let parts: Vec<&str> = element.split(',').collect();
        if parts.len() >= 2 {
            let price = parts[0].trim_matches('"').parse::<f64>().unwrap_or(0.0);
            let qty = parts[1].trim_matches('"').parse::<f64>().unwrap_or(0.0);
            result.push((price, qty));
        }
    }

    result
}

pub fn parse_futures_depth_update(input: &[u8]) -> Option<FuturesDepthUpdate<'_>> {
    // 解析事件时间
    let event_time_pattern = b"\"E\":";
    let start = memchr::memmem::find(input, event_time_pattern)?;
    let start = start + event_time_pattern.len();
    let end = memchr::memchr(b',', &input[start..])?;
    let event_time_str = &input[start..start + end];

    // 解析交易时间
    let transaction_time_pattern = b"\"T\":";
    let start = memchr::memmem::find(input, transaction_time_pattern)?;
    let start = start + transaction_time_pattern.len();
    let end = memchr::memchr(b',', &input[start..])?;
    let transaction_time_str = &input[start..start + end];

    // 解析交易对符号
    let symbol_pattern = b"\"s\":\"";
    let start = memchr::memmem::find(input, symbol_pattern)?;
    let start = start + symbol_pattern.len();
    let end = memchr::memchr(b'"', &input[start..])?;
    let symbol = &input[start..start + end];

    // 解析第一个更新ID
    let first_update_id_pattern = b"\"U\":";
    let start = memchr::memmem::find(input, first_update_id_pattern)?;
    let start = start + first_update_id_pattern.len();
    let end = memchr::memchr(b',', &input[start..])?;
    let first_update_id_str = &input[start..start + end];

    // 解析最终更新ID
    let final_update_id_pattern = b"\"u\":";
    let start = memchr::memmem::find(input, final_update_id_pattern)?;
    let start = start + final_update_id_pattern.len();
    let end = memchr::memchr(b',', &input[start..])?;
    let final_update_id_str = &input[start..start + end];

    // 解析前一个更新ID
    let prev_update_id_pattern = b"\"pu\":";
    let start = memchr::memmem::find(input, prev_update_id_pattern)?;
    let start = start + prev_update_id_pattern.len();
    let end = memchr::memchr(b',', &input[start..])?;
    let prev_update_id_str = &input[start..start + end];

    // 解析买单更新
    let bids_pattern = b"\"b\":[";
    let bids_start = memchr::memmem::find(input, bids_pattern)?;
    let bids_start = bids_start + bids_pattern.len();

    // 找到匹配的数组结束括号
    let mut bracket_count = 1;
    let mut bids_end = bids_start;
    for (i, &byte) in input[bids_start..].iter().enumerate() {
        if byte == b'[' {
            bracket_count += 1;
        } else if byte == b']' {
            bracket_count -= 1;
            if bracket_count == 0 {
                bids_end = bids_start + i;
                break;
            }
        }
    }
    let bids_section = &input[bids_start..bids_end];
    let bid_updates = parse_price_levels_update(bids_section);

    // 解析卖单更新
    let asks_pattern = b"\"a\":[";
    let asks_start = memchr::memmem::find(input, asks_pattern)?;
    let asks_start = asks_start + asks_pattern.len();

    // 找到匹配的数组结束括号
    let mut bracket_count = 1;
    let mut asks_end = asks_start;
    for (i, &byte) in input[asks_start..].iter().enumerate() {
        if byte == b'[' {
            bracket_count += 1;
        } else if byte == b']' {
            bracket_count -= 1;
            if bracket_count == 0 {
                asks_end = asks_start + i;
                break;
            }
        }
    }
    let asks_section = &input[asks_start..asks_end];
    let ask_updates = parse_price_levels_update(asks_section);

    // 转换字符串为数字
    let event_time = unsafe { std::str::from_utf8_unchecked(event_time_str) }
        .parse::<u64>()
        .unwrap_or(0);
    let transaction_time = unsafe { std::str::from_utf8_unchecked(transaction_time_str) }
        .parse::<u64>()
        .unwrap_or(0);
    let first_update_id = unsafe { std::str::from_utf8_unchecked(first_update_id_str) }
        .parse::<u64>()
        .unwrap_or(0);
    let final_update_id = unsafe { std::str::from_utf8_unchecked(final_update_id_str) }
        .parse::<u64>()
        .unwrap_or(0);
    let prev_update_id = unsafe { std::str::from_utf8_unchecked(prev_update_id_str) }
        .parse::<u64>()
        .unwrap_or(0);
    let symbol = unsafe { std::str::from_utf8_unchecked(symbol) };

    Some(FuturesDepthUpdate {
        symbol,
        event_time,
        transaction_time,
        first_update_id,
        final_update_id,
        prev_update_id,
        bid_updates,
        ask_updates,
    })
}

#[derive(Debug, Clone)]
pub struct DepthUpdate<'a> {
    pub symbol: &'a str,
    pub event_time: u64,
    pub final_update_id: u64,
    pub bid_updates: Vec<(f64, f64)>,
    pub ask_updates: Vec<(f64, f64)>,
}

pub fn parse_depth_update(input: &[u8]) -> Option<DepthUpdate<'_>> {
    let event_time_pattern = b"\"E\":";
    let start = memchr::memmem::find(input, event_time_pattern)?;
    let start = start + event_time_pattern.len();
    let end = memchr::memchr(b',', &input[start..])?;
    let event_time_str = &input[start..start + end];
    let event_time = unsafe { from_utf8_unchecked(event_time_str) }
        .parse()
        .unwrap();

    let symbol_pattern = b"\"s\":\"";
    let start = memchr::memmem::find(input, symbol_pattern)?;
    let start = start + symbol_pattern.len();
    let end = memchr::memchr(b'\"', &input[start..])?;
    let symbol_str = &input[start..start + end];

    let final_update_id_parttern = b"\"u\":";
    let start = memchr::memmem::find(input, final_update_id_parttern)?;
    let start = start + final_update_id_parttern.len();
    let end = memchr::memchr(b',', &input[start..])?;
    let final_update_id_str = &input[start..start + end];

    let bids_pattern = b"\"b\":[[";
    let bids_end_pattern = b"]]";
    let bids = match memchr::memmem::find(input, bids_pattern) {
        Some(start) => {
            let start = start + bids_pattern.len();
            let end = memchr::memmem::find(&input[start..], bids_end_pattern)?;
            let bids_str = &input[start..start + end];
            parse_price_levels(bids_str)
        }
        None => Vec::new(),
    };

    let asks_pattern = b"\"a\":[[";
    let asks_end_pattern = b"]]";
    let asks = match memchr::memmem::find(input, asks_pattern) {
        Some(start) => {
            let start = start + asks_pattern.len();
            let end = memchr::memmem::find(&input[start..], asks_end_pattern)?;
            let asks_str = &input[start..start + end];
            parse_price_levels(asks_str)
        }
        None => Vec::new(),
    };

    let symbol = unsafe { from_utf8_unchecked(symbol_str) };
    let final_update_id = unsafe { from_utf8_unchecked(final_update_id_str) }
        .parse()
        .unwrap();

    Some(DepthUpdate {
        symbol,
        event_time,
        final_update_id,
        bid_updates: bids,
        ask_updates: asks,
    })
}

fn parse_price_levels(data: &[u8]) -> Vec<(f64, f64)> {
    let mut result = Vec::new();
    let mut start = 0;
    loop {
        start = match memchr::memchr(b'"', &data[start..]) {
            Some(pos) => pos + start,
            None => break,
        };
        let end = match memchr::memchr(b'"', &data[start + 1..]) {
            Some(pos) => pos + start + 1,
            None => break,
        };
        let price_str = unsafe { from_utf8_unchecked(&data[start + 1..end]) };
        let price = price_str.parse().unwrap();
        result.push((price, 0.0));
        start = end + 1;
        let end = match memchr::memchr(b'[', &data[start..]) {
            Some(pos) => pos + start,
            None => break,
        };
        start = end + 1;
    }
    result
}

#[cfg(test)]
mod tests {
    use super::*;

    #[test]
    fn test_parse_futures_depth_update() {
        let test_data = r#"{"e":"depthUpdate","E":1571889248277,"T":1571889248276,"s":"BTCUSDT","U":390497796,"u":390497878,"pu":390497794,"b":[["7403.89","0.002"],["7403.90","3.906"],["7404.00","1.428"]],"a":[["7405.96","3.340"],["7406.63","4.525"],["7407.08","2.475"]]}"#;

        let result = parse_futures_depth_update(test_data.as_bytes());
        assert!(result.is_some());

        let update = result.unwrap();
        assert_eq!(update.event_time, 1571889248277);
        assert_eq!(update.transaction_time, 1571889248276);
        assert_eq!(update.symbol, "BTCUSDT");
        assert_eq!(update.first_update_id, 390497796);
        assert_eq!(update.final_update_id, 390497878);
        assert_eq!(update.prev_update_id, 390497794);

        // 检查买单更新
        assert_eq!(update.bid_updates.len(), 3);
        assert_eq!(update.bid_updates[0], (7403.89, 0.002));
        assert_eq!(update.bid_updates[1], (7403.90, 3.906));
        assert_eq!(update.bid_updates[2], (7404.00, 1.428));

        // 检查卖单更新
        assert_eq!(update.ask_updates.len(), 3);
        assert_eq!(update.ask_updates[0], (7405.96, 3.340));
        assert_eq!(update.ask_updates[1], (7406.63, 4.525));
        assert_eq!(update.ask_updates[2], (7407.08, 2.475));
    }

    #[test]
    fn test_parse_futures_depth_update_empty_arrays() {
        let test_data = r#"{"e":"depthUpdate","E":1571889248277,"T":1571889248276,"s":"BTCUSDT","U":390497796,"u":390497878,"pu":390497794,"b":[],"a":[]}"#;

        let result = parse_futures_depth_update(test_data.as_bytes());
        assert!(result.is_some());

        let update = result.unwrap();
        assert_eq!(update.bid_updates.len(), 0);
        assert_eq!(update.ask_updates.len(), 0);
    }

    #[test]
    fn test_parse_price_levels_update() {
        let test_data = r#"[["7403.89","0.002"],["7403.90","3.906"]]"#;
        let result = parse_price_levels_update(test_data.as_bytes());

        assert_eq!(result.len(), 2);
        assert_eq!(result[0], (7403.89, 0.002));
        assert_eq!(result[1], (7403.90, 3.906));
    }
}
