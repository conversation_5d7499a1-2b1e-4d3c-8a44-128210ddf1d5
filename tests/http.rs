extern crate env_logger;
extern crate libwebsocket_rs;
extern crate log;

use libwebsocket_rs::CallbackData;
use libwebsocket_rs::Message;
use libwebsocket_rs::Settings;
use libwebsocket_rs::WebSocket;
use libwebsocket_rs::WebSocketHandle;
use libwebsocket_rs::net::message::http::HttpRequest;
use libwebsocket_rs::net::message::http::StatusCode;
use log::LevelFilter;
use log::info;
use mio::Token;
use serde::Deserialize;
use std::cell::RefCell;
use std::ops::Add;
use std::rc::Rc;

#[derive(Debug, Deserialize)]
struct ExchangeInfo {
    timezone: String,
    serverTime: u64,
    rateLimits: Vec<RateLimit>,
    exchangeFilters: Vec<String>,
    symbols: Vec<Symbol>,
}

#[derive(Debug, Deserialize)]
struct RateLimit {
    rateLimitType: String,
    interval: String,
    limit: u64,
}

#[derive(Debug, Deserialize)]
struct Symbol {
    symbol: String,
    status: String,
    baseAsset: String,
    baseAssetPrecision: u64,
}
#[test]
fn test_http() {
    let _ = env_logger::builder()
            .is_test(true)  // ensures logs are printed only during tests
            .filter_level(LevelFilter::Trace)  // sets the log level to Info
            .try_init();

    let recv_times = Rc::new(RefCell::new(0));
    let recv_times_clone = recv_times.clone();
    const N: usize = 1024 * 32;
    const OUT_LEN: usize = 1024 * 4;
    let callback = move |_handle: &mut WebSocketHandle<N, OUT_LEN>, data: CallbackData| {
        match data {
            CallbackData::Message(_, response) => match response {
                Message::HttpResponse(response) => {
                    assert_eq!(response.status, Some(StatusCode::OK));
                    _handle.stop();
                    info!(
                        "on_response_callback, res headers: {:?}, body_len: {:?}",
                        response.headers,
                        response.body.as_ref().unwrap().len()
                    );
                    let mut rc = recv_times_clone.as_ref().borrow_mut();
                    *rc = rc.add(1);
                    let exchange_info =
                        serde_json::from_slice::<ExchangeInfo>(&response.body.as_ref().unwrap());
                    match exchange_info {
                        Ok(exchange_info) => {
                            info!("exchange_info: {:?}", exchange_info.symbols.len())
                        }
                        Err(e) => {
                            info!("error: {:?} from", e.io_error_kind(),)
                        }
                    }
                }
                _ => assert!(false, "Unexpected message type"),
            },
            CallbackData::ConnectionOpen(_) => {
                info!("Connection opened");
            }
            _ => assert!(false, "Unexpected message type"),
        }
        Ok(())
    };
    let settings = Settings::default();
    let mut websocket = WebSocket::new(settings, callback).unwrap();
    let token = Token(0);
    if let Err(e) = websocket.connect("https://www.baidu.com", token) {
        panic!("Failed to connect: {:?}", e);
    }
    websocket
        .handle_mut()
        .send_message(token, Message::HttpRequest(HttpRequest::from("/")))
        .unwrap();
    websocket.run().unwrap();
    assert_eq!(*recv_times.borrow(), 1);
}

// #[test]
// fn test_http_2_parallel() {
//     let _ = env_logger::builder()
//             .is_test(true)  // ensures logs are printed only during tests
//             .filter_level(LevelFilter::Trace)  // sets the log level to Info
//             .try_init();
//     let settings = Settings::default();
//     let mut websocket = WebSocket::new(settings);
//     let url = url::Url::parse("https://www.baidu.com").unwrap();
//     let config = ConnectionConfig::Http(url);
//     let http_handle = websocket.connect(config).unwrap();
//     let response_count = Rc::new(RefCell::new(0));
//     let response_count_clone = response_count.clone();
//     websocket
//         .set_on_http_response_cb(http_handle, move |response| {
//             info!(
//                 "on_response_callback, res len: {:?}",
//                 response.body().unwrap().len()
//             );
//             assert_eq!(response.status(), 200);
//             let mut rc = response_count_clone.borrow_mut();
//             *rc += 1;
//             return Ok(Some(vec![(http_handle, Message::HttpClose)]));
//         })
//         .unwrap();
//     websocket
//         .send_message(
//             http_handle,
//             Message::HttpRequest("/".into_request().unwrap()),
//         )
//         .unwrap();
//     assert!(
//         websocket
//             .send_message(
//                 http_handle,
//                 Message::HttpRequest("/".into_request().unwrap()),
//             )
//             .is_err()
//     );
//     websocket.run(false).unwrap();
//     assert_eq!(*response_count.borrow(), 1);
// }

// #[test]
// fn test_http_header_and_body() {
//     let _ = env_logger::builder()
//             .is_test(true)  // ensures logs are printed only during tests
//             .filter_level(LevelFilter::Trace)  // sets the log level to Info
//             .try_init();
//     let settings = Settings::default();
//     let mut websocket = WebSocket::new(settings);
//     let url = url::Url::parse("https://www.baidu.com").unwrap();
//     let config = ConnectionConfig::Http(url);
//     let http_handle = websocket.connect(config).unwrap();
//     let response_count = Rc::new(RefCell::new(0));
//     let response_count_clone = response_count.clone();
//     websocket
//         .set_on_http_response_cb(http_handle, move |response| {
//             info!("on_response_callback, res: {:?}", response);
//             assert_eq!(response.status(), 200);
//             assert_eq!(response.body().as_ref().unwrap().len() > 1024, true);
//             let mut rc = response_count_clone.borrow_mut();
//             *rc += 1;
//             Ok(Some(vec![(http_handle, Message::HttpClose)]))
//         })
//         .unwrap();
//     let req = "/".into_request().unwrap();
//     websocket
//         .send_message(http_handle, Message::HttpRequest(req))
//         .unwrap();
//     websocket.run(false).unwrap();
//     assert_eq!(*response_count.borrow(), 1);
// }
